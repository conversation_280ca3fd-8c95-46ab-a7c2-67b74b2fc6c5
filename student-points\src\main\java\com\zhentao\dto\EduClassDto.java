package com.zhentao.dto;

import com.zhentao.pojo.EduStudent;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EduClassDto {
    /**
     * 班级ID
     */
    private Integer classId;

    private List<EduStudent> students;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 所属阶段ID
     */
    private Integer stageId;

    /**
     * 教室号
     */
    private String classroom;

    /**
     * 讲师ID
     */
    private Integer teacherId;

    /**
     * 导员ID
     */
    private Integer counselorId;

    /**
     * 阶段主任ID
     */
    private Integer directorId;

    /**
     * 开班日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 状态（0-正常，1-结束）
     */
    private Integer status;

    /**
     * 创建者ID
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者ID
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0-存在，1-删除）
     */
    private Integer delFlag;
}

<template>
  <div class="student-info-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>学生明细</h2>
      <p>查看和管理个人学习情况、积分详情等信息</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：学生信息卡片 -->
      <el-col :span="8">
        <el-card class="student-profile-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>学生信息</span>
            </div>
          </template>
          <div class="profile-content">
            <div class="avatar-section">
              <el-avatar @click="changeAvatar" :size="80" :src="studentProfile.avatar">
                {{ studentProfile.realName }}
              </el-avatar>
            </div>
            <div class="info-section">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ studentProfile.realName }}</span>
              </div>
              <div class="info-item">
                <label>学号：</label>
                <span>{{ studentProfile.studentNo }}</span>
              </div>
              <div class="info-item">
                <label>班级：</label>
                <span>{{ studentProfile.className }}</span>
              </div>
              <div class="info-item">
                <label>阶段：</label>
                <span>{{ studentProfile.stageName }}</span>
              </div>
              <div class="info-item">
                <label>联系电话：</label>
                <span>{{ studentProfile.phone }}</span>
              </div>
              <div class="info-item">
                <label>邮箱：</label>
                <span>{{ studentProfile.email }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 积分统计卡片 -->
        <el-card class="points-stats-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>积分统计</span>
            </div>
          </template>
          <div class="stats-content">
            <div class="stat-item large">
              <div class="stat-value">{{ studentProfile.points }}</div>
              <div class="stat-label">总积分</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：详细信息 -->
      <el-col :span="16">
        <!-- 功能标签页 -->
        <el-card class="info-tabs-card" shadow="hover">
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 积分记录 -->
            <el-tab-pane label="积分记录" name="points">
              <div class="points-records">
                <div class="records-header">
                  <el-form :inline="true" :model="pointsFilter" size="small">
                    <el-form-item label="时间范围">
                      <el-date-picker
                        v-model="pointsFilter.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        size="small"
                      />
                    </el-form-item>
                    <el-form-item label="类型">
                      <el-select v-model="pointsFilter.type" placeholder="全部" size="small">
                        <el-option label="全部" value="" />
                        <el-option label="加分" value="add" />
                        <el-option label="扣分" value="deduct" />
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" size="small" @click="searchPointsRecords">查询</el-button>
                      <el-button size="small" @click="resetPointsFilter">重置</el-button>
                    </el-form-item>
                  </el-form>
                </div>
                <el-table
                  :data="pointsRecords"
                  stripe
                  v-loading="loading"
                  element-loading-text="加载积分记录中..."
                  empty-text="暂无积分记录"
                >
                  <el-table-column prop="date" label="日期" width="120" />
                  <el-table-column prop="type" label="类型" width="80">
                    <template #default="scope">
                      <el-tag :type="scope.row.type === 'add' ? 'success' : 'danger'" size="small">
                        {{ scope.row.type === 'add' ? '加分' : '扣分' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="reason" label="原因" show-overflow-tooltip />
                  <el-table-column prop="points" label="分值" width="80">
                    <template #default="scope">
                      <span :class="scope.row.type === 'add' ? 'points-add' : 'points-deduct'">
                        {{ scope.row.type === 'add' ? '+' : '-' }}{{ scope.row.points }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="operator" label="操作人" width="100" show-overflow-tooltip />
                  <el-table-column prop="status" label="状态" width="80">
                    <template #default="scope">
                      <el-tag :type="getStatusType(scope.row.status)" size="small">
                        {{ getStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="pagination-wrapper">
                  <el-pagination
                    v-model:current-page="pointsPagination.currentPage"
                    v-model:page-size="pointsPagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="pointsPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handlePointsPageSizeChange"
                    @current-change="handlePointsPageChange"
                  />
                </div>
              </div>
            </el-tab-pane>

            <!-- 活动参与 -->
            <el-tab-pane label="活动参与" name="activities">
              <div class="activities-info">
                <div class="activities-stats">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <div class="activity-stat-item">
                        <el-icon class="stat-icon"><Calendar /></el-icon>
                        <div class="stat-info">
                          <div class="stat-value">{{ activityPagination.total }}</div>
                          <div class="stat-label">参与活动总数</div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <el-table
                  :data="activityRecords"
                  stripe
                  v-loading="activityLoading"
                  element-loading-text="加载活动记录中..."
                  empty-text="暂无活动记录"
                >
                  <el-table-column prop="date" label="开始日期" width="120" />
                  <el-table-column prop="activityName" label="活动名称"  width="120" />
                  <el-table-column prop="activityType" label="活动描述" show-overflow-tooltip />
                  <el-table-column prop="role" label="创办人" width="100" />
                </el-table>
                <div class="pagination-wrapper">
                  <el-pagination
                    v-model:current-page="activityPagination.currentPage"
                    v-model:page-size="activityPagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="activityPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleActivityPageSizeChange"
                    @current-change="handleActivityPageChange"
                  />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  User, TrendCharts, Calendar, Trophy, Clock
} from '@element-plus/icons-vue'
import { getPointsApplyList, getActivityStudentList } from '@/api/student/application.js';
import { ElMessage } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('points')

// 学生档案信息
const studentProfile = JSON.parse(localStorage.getItem("studentList"));

// 积分记录筛选
const pointsFilter = reactive({
  dateRange: [],
  type: ''
})

// 积分记录分页
const pointsPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 积分记录数据
const pointsRecords = ref([])
const loading = ref(false)


// 活动统计
const activityStats = reactive({
  totalActivities: 0,
  awards: 0,
  totalHours: 0
})

// 活动记录
const activityRecords = ref([])
const activityLoading = ref(false)

// 活动记录分页
const activityPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取积分记录数据
const fetchPointsRecords = async () => {
  if (!studentProfile?.studentNo) {
    ElMessage.error('学生信息不完整，无法获取积分记录')
    return
  }

  loading.value = true
  try {
    const params = {
      studentNo: studentProfile.studentNo,
      pageNum: pointsPagination.currentPage,
      pageSize: pointsPagination.pageSize
    }

    // 添加时间范围筛选
    if (pointsFilter.dateRange && pointsFilter.dateRange.length === 2) {
      params.startTime = pointsFilter.dateRange[0]
      params.endTime = pointsFilter.dateRange[1]
    }

    // 添加类型筛选
    if (pointsFilter.type) {
      params.pointsChange = pointsFilter.type === 'add' ? 1 : 2
    }

    const response = await getPointsApplyList(params)

    if (response.data && response.data.records) {
      // 转换数据格式以适配前端表格
      pointsRecords.value = response.data.records.map(record => ({
        date: record.createTime ? record.createTime.split(' ')[0] : '',
        type: record.pointsChange === 1 ? 'add' : 'deduct',
        reason: record.reason || '',
        points: record.points || 0,
        operator: record.createUser?.realName || record.applyUser?.realName || '系统',
        status: getRecordStatus(record),
        originalRecord: record // 保留原始数据以备后用
      }))

      pointsPagination.total = response.data.total || 0
    } else {
      pointsRecords.value = []
      pointsPagination.total = 0
    }
  } catch (error) {
    console.error('获取积分记录失败:', error)
    ElMessage.error('获取积分记录失败，请稍后重试')
    pointsRecords.value = []
    pointsPagination.total = 0
  } finally {
    loading.value = false
  }
}

// 根据审核状态确定记录状态
const getRecordStatus = (record) => {
  // 如果任何一个审核状态为拒绝(3)，则整体为拒绝
  if (record.status === 3 || record.status1 === 3 || record.status2 === 3) {
    return 'rejected'
  }
  // 如果所有审核状态都通过(2)，则为通过
  if (record.status === 2 && record.status1 === 2 && record.status2 === 2) {
    return 'approved'
  }
  // 否则为待审核
  return 'pending'
}

// 获取活动记录数据
const fetchActivityRecords = async () => {
  if (!studentProfile?.studentId && !studentProfile?.studentNo) {
    ElMessage.error('学生信息不完整，无法获取活动记录')
    return
  }

  activityLoading.value = true
  try {
    const params = {
      studentNo: studentProfile.studentId || studentProfile.studentNo, // 后端期望的是学生ID
      pageNum: activityPagination.currentPage,
      pageSize: activityPagination.pageSize
    }

    const response = await getActivityStudentList(params)
    console.log('活动记录响应:', response)

    if (response.data && response.data.data.records) {
      // 转换数据格式以适配前端表格
      activityRecords.value = response.data.data.records.map(record => ({
        date: record.createTime ? record.createTime.split(' ')[0] : '',
        activityName: record.activity?.activityName || '未知活动',
        role: record.activity.createUser.realName || '参与者',
        activityType: record.activity?.activityText || '未知活动类型',
      }))

      activityPagination.total = response.data.data.total || 0

      // 更新活动统计
      updateActivityStats(response.data.data.records)
    } else {
      activityRecords.value = []
      activityPagination.total = 0
      // 重置统计
      activityStats.totalActivities = 0
      activityStats.awards = 0
      activityStats.totalHours = 0
    }
  } catch (error) {
    console.error('获取活动记录失败:', error)
    ElMessage.error('获取活动记录失败，请稍后重试')
    activityRecords.value = []
    activityPagination.total = 0
  } finally {
    activityLoading.value = false
  }
}


// 更新活动统计
const updateActivityStats = (records) => {
  activityStats.totalActivities = records.length
  activityStats.awards = records.filter(record =>
    record.award && record.award !== '无' && record.award !== ''
  ).length

  // 计算总时长（简单估算，每个活动按平均4小时计算）
  activityStats.totalHours = records.length * 4
}

// 方法
const editProfile = () => {
  console.log('编辑个人信息')
}

const changeAvatar = () => {
  console.log('更换头像')
}

const searchPointsRecords = () => {
  console.log('搜索积分记录')
  pointsPagination.currentPage = 1 // 重置到第一页
  fetchPointsRecords()
}

const resetPointsFilter = () => {
  pointsFilter.dateRange = []
  pointsFilter.type = ''
  pointsPagination.currentPage = 1 // 重置到第一页
  fetchPointsRecords()
}

const handlePointsPageSizeChange = (size) => {
  pointsPagination.pageSize = size
  pointsPagination.currentPage = 1 // 重置到第一页
  fetchPointsRecords()
}

const handlePointsPageChange = (page) => {
  pointsPagination.currentPage = page
  fetchPointsRecords()
}

// 活动记录分页处理
const handleActivityPageSizeChange = (size) => {
  activityPagination.pageSize = size
  activityPagination.currentPage = 1 // 重置到第一页
  fetchActivityRecords()
}

const handleActivityPageChange = (page) => {
  activityPagination.currentPage = page
  fetchActivityRecords()
}

const getStatusType = (status) => {
  const statusMap = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 组件挂载时初始化数据
onMounted(() => {
  if (studentProfile?.studentNo) {
    fetchPointsRecords()
    fetchActivityRecords()
  } else {
    ElMessage.warning('未找到学生信息，请重新登录')
  }
})
</script>

<style scoped>
.student-info-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 学生档案卡片 */
.student-profile-card {
  margin-bottom: 20px;
}

.profile-content {
  text-align: center;
}

.avatar-section {
  margin-bottom: 20px;
}

.avatar-section .el-button {
  margin-top: 10px;
}

.info-section {
  text-align: left;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.info-item label {
  width: 80px;
  color: #606266;
  font-size: 14px;
}

.info-item span {
  flex: 1;
  color: #303133;
}

/* 积分统计卡片 */
.points-stats-card {
  margin-bottom: 20px;
}

.stats-content {
  text-align: center;
}

.stat-item.large {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.stat-item.large .stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-item.large .stat-label {
  font-size: 16px;
  color: #606266;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #909399;
}

/* 信息标签页卡片 */
.info-tabs-card {
  min-height: 600px;
}

/* 积分记录 */
.records-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.points-add {
  color: #67c23a;
  font-weight: bold;
}

.points-deduct {
  color: #f56c6c;
  font-weight: bold;
}

/* 学习情况 */
.study-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.study-stat-item {
  text-align: center;
}

.study-stat-item .stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.study-stat-item .stat-label {
  font-size: 14px;
  color: #606266;
}

/* 成绩颜色 */
.score-excellent { color: #67c23a; font-weight: bold; }
.score-good { color: #409eff; font-weight: bold; }
.score-average { color: #e6a23c; }
.score-pass { color: #909399; }
.score-fail { color: #f56c6c; font-weight: bold; }

/* 活动参与 */
.activities-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 32px;
  color: #409eff;
}

.stat-info .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-info .stat-label {
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .records-header .el-form {
    display: block;
  }

  .records-header .el-form-item {
    margin-bottom: 10px;
  }

  .activity-stat-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<template>
  <div class="student-info-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>学生明细</h2>
      <p>查看和管理个人学习情况、积分详情等信息</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：学生信息卡片 -->
      <el-col :span="8">
        <el-card class="student-profile-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>学生信息</span>
              <el-button type="text" size="small" @click="editProfile">编辑</el-button>
            </div>
          </template>
          <div class="profile-content">
            <div class="avatar-section">
              <el-avatar :size="80" :src="studentProfile.avatar">
                {{ studentProfile.name.charAt(0) }}
              </el-avatar>
              <el-button type="text" size="small" @click="changeAvatar">更换头像</el-button>
            </div>
            <div class="info-section">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ studentProfile.name }}</span>
              </div>
              <div class="info-item">
                <label>学号：</label>
                <span>{{ studentProfile.studentNo }}</span>
              </div>
              <div class="info-item">
                <label>班级：</label>
                <span>{{ studentProfile.className }}</span>
              </div>
              <div class="info-item">
                <label>专业：</label>
                <span>{{ studentProfile.major }}</span>
              </div>
              <div class="info-item">
                <label>年级：</label>
                <span>{{ studentProfile.grade }}</span>
              </div>
              <div class="info-item">
                <label>联系电话：</label>
                <span>{{ studentProfile.phone }}</span>
              </div>
              <div class="info-item">
                <label>邮箱：</label>
                <span>{{ studentProfile.email }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 积分统计卡片 -->
        <el-card class="points-stats-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>积分统计</span>
            </div>
          </template>
          <div class="stats-content">
            <div class="stat-item large">
              <div class="stat-value">{{ pointsStats.totalPoints }}</div>
              <div class="stat-label">总积分</div>
            </div>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ pointsStats.monthPoints }}</div>
                <div class="stat-label">本月积分</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ pointsStats.classRank }}</div>
                <div class="stat-label">班级排名</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ pointsStats.gradeRank }}</div>
                <div class="stat-label">年级排名</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ pointsStats.averagePoints }}</div>
                <div class="stat-label">平均分</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：详细信息 -->
      <el-col :span="16">
        <!-- 功能标签页 -->
        <el-card class="info-tabs-card" shadow="hover">
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 积分记录 -->
            <el-tab-pane label="积分记录" name="points">
              <div class="points-records">
                <div class="records-header">
                  <el-form :inline="true" :model="pointsFilter" size="small">
                    <el-form-item label="时间范围">
                      <el-date-picker
                        v-model="pointsFilter.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        size="small"
                      />
                    </el-form-item>
                    <el-form-item label="类型">
                      <el-select v-model="pointsFilter.type" placeholder="全部" size="small">
                        <el-option label="全部" value="" />
                        <el-option label="加分" value="add" />
                        <el-option label="扣分" value="deduct" />
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" size="small" @click="searchPointsRecords">查询</el-button>
                      <el-button size="small" @click="resetPointsFilter">重置</el-button>
                    </el-form-item>
                  </el-form>
                </div>
                <el-table :data="pointsRecords" stripe>
                  <el-table-column prop="date" label="日期" width="120" />
                  <el-table-column prop="type" label="类型" width="80">
                    <template #default="scope">
                      <el-tag :type="scope.row.type === 'add' ? 'success' : 'danger'" size="small">
                        {{ scope.row.type === 'add' ? '加分' : '扣分' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="reason" label="原因" />
                  <el-table-column prop="points" label="分值" width="80">
                    <template #default="scope">
                      <span :class="scope.row.type === 'add' ? 'points-add' : 'points-deduct'">
                        {{ scope.row.type === 'add' ? '+' : '-' }}{{ scope.row.points }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="operator" label="操作人" width="100" />
                  <el-table-column prop="status" label="状态" width="80">
                    <template #default="scope">
                      <el-tag :type="getStatusType(scope.row.status)" size="small">
                        {{ getStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="pagination-wrapper">
                  <el-pagination
                    v-model:current-page="pointsPagination.currentPage"
                    v-model:page-size="pointsPagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="pointsPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handlePointsPageSizeChange"
                    @current-change="handlePointsPageChange"
                  />
                </div>
              </div>
            </el-tab-pane>

            <!-- 学习情况 -->
            <el-tab-pane label="学习情况" name="study">
              <div class="study-info">
                <div class="study-stats">
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <div class="study-stat-item">
                        <div class="stat-value">{{ studyStats.gpa }}</div>
                        <div class="stat-label">平均绩点</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="study-stat-item">
                        <div class="stat-value">{{ studyStats.totalCredits }}</div>
                        <div class="stat-label">总学分</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="study-stat-item">
                        <div class="stat-value">{{ studyStats.passedCourses }}</div>
                        <div class="stat-label">通过课程</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="study-stat-item">
                        <div class="stat-value">{{ studyStats.failedCourses }}</div>
                        <div class="stat-label">挂科课程</div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <el-table :data="courseRecords" stripe>
                  <el-table-column prop="semester" label="学期" width="120" />
                  <el-table-column prop="courseName" label="课程名称" />
                  <el-table-column prop="courseType" label="课程类型" width="100" />
                  <el-table-column prop="credits" label="学分" width="80" />
                  <el-table-column prop="score" label="成绩" width="80">
                    <template #default="scope">
                      <span :class="getScoreClass(scope.row.score)">{{ scope.row.score }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="gpa" label="绩点" width="80" />
                  <el-table-column prop="teacher" label="任课教师" width="100" />
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 活动参与 -->
            <el-tab-pane label="活动参与" name="activities">
              <div class="activities-info">
                <div class="activities-stats">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <div class="activity-stat-item">
                        <el-icon class="stat-icon"><Calendar /></el-icon>
                        <div class="stat-info">
                          <div class="stat-value">{{ activityStats.totalActivities }}</div>
                          <div class="stat-label">参与活动总数</div>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="activity-stat-item">
                        <el-icon class="stat-icon"><Trophy /></el-icon>
                        <div class="stat-info">
                          <div class="stat-value">{{ activityStats.awards }}</div>
                          <div class="stat-label">获得奖项</div>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="activity-stat-item">
                        <el-icon class="stat-icon"><Clock /></el-icon>
                        <div class="stat-info">
                          <div class="stat-value">{{ activityStats.totalHours }}</div>
                          <div class="stat-label">活动时长(小时)</div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <el-table :data="activityRecords" stripe>
                  <el-table-column prop="date" label="日期" width="120" />
                  <el-table-column prop="activityName" label="活动名称" />
                  <el-table-column prop="activityType" label="活动类型" width="120" />
                  <el-table-column prop="role" label="参与角色" width="100" />
                  <el-table-column prop="duration" label="时长" width="80" />
                  <el-table-column prop="award" label="获得奖项" width="120" />
                  <el-table-column prop="points" label="获得积分" width="80">
                    <template #default="scope">
                      <span class="points-add">+{{ scope.row.points }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { 
  User, TrendCharts, Calendar, Trophy, Clock 
} from '@element-plus/icons-vue'

// 当前激活的标签页
const activeTab = ref('points')

// 学生档案信息
const studentProfile = reactive({
  name: '张三',
  studentNo: '2021001',
  className: '计算机科学与技术1班',
  major: '计算机科学与技术',
  grade: '2021级',
  phone: '138****8888',
  email: '<EMAIL>',
  avatar: ''
})

// 积分统计
const pointsStats = reactive({
  totalPoints: 1250,
  monthPoints: 180,
  classRank: 3,
  gradeRank: 15,
  averagePoints: 85.5
})

// 积分记录筛选
const pointsFilter = reactive({
  dateRange: [],
  type: ''
})

// 积分记录分页
const pointsPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 50
})

// 积分记录数据
const pointsRecords = ref([
  {
    date: '2024-12-20',
    type: 'add',
    reason: '期末考试成绩优秀',
    points: 20,
    operator: '李老师',
    status: 'approved'
  },
  {
    date: '2024-12-18',
    type: 'add',
    reason: '参与志愿服务活动',
    points: 15,
    operator: '王老师',
    status: 'approved'
  },
  {
    date: '2024-12-15',
    type: 'deduct',
    reason: '迟到',
    points: 5,
    operator: '张老师',
    status: 'approved'
  }
])

// 学习统计
const studyStats = reactive({
  gpa: 3.75,
  totalCredits: 120,
  passedCourses: 24,
  failedCourses: 1
})

// 课程记录
const courseRecords = ref([
  {
    semester: '2024-1',
    courseName: '数据结构与算法',
    courseType: '专业必修',
    credits: 4,
    score: 92,
    gpa: 4.0,
    teacher: '李教授'
  },
  {
    semester: '2024-1',
    courseName: '计算机网络',
    courseType: '专业必修',
    credits: 3,
    score: 85,
    gpa: 3.5,
    teacher: '王教授'
  }
])

// 活动统计
const activityStats = reactive({
  totalActivities: 15,
  awards: 3,
  totalHours: 120
})

// 活动记录
const activityRecords = ref([
  {
    date: '2024-12-10',
    activityName: '校园文化节',
    activityType: '文艺活动',
    role: '参与者',
    duration: '4小时',
    award: '优秀参与奖',
    points: 10
  },
  {
    date: '2024-11-25',
    activityName: '编程竞赛',
    activityType: '学科竞赛',
    role: '参赛者',
    duration: '6小时',
    award: '三等奖',
    points: 25
  }
])

// 方法
const editProfile = () => {
  console.log('编辑个人信息')
}

const changeAvatar = () => {
  console.log('更换头像')
}

const searchPointsRecords = () => {
  console.log('搜索积分记录')
}

const resetPointsFilter = () => {
  pointsFilter.dateRange = []
  pointsFilter.type = ''
}

const handlePointsPageSizeChange = (size) => {
  pointsPagination.pageSize = size
}

const handlePointsPageChange = (page) => {
  pointsPagination.currentPage = page
}

const getStatusType = (status) => {
  const statusMap = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 70) return 'score-average'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}
</script>

<style scoped>
.student-info-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 学生档案卡片 */
.student-profile-card {
  margin-bottom: 20px;
}

.profile-content {
  text-align: center;
}

.avatar-section {
  margin-bottom: 20px;
}

.avatar-section .el-button {
  margin-top: 10px;
}

.info-section {
  text-align: left;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.info-item label {
  width: 80px;
  color: #606266;
  font-size: 14px;
}

.info-item span {
  flex: 1;
  color: #303133;
}

/* 积分统计卡片 */
.points-stats-card {
  margin-bottom: 20px;
}

.stats-content {
  text-align: center;
}

.stat-item.large {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.stat-item.large .stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-item.large .stat-label {
  font-size: 16px;
  color: #606266;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #909399;
}

/* 信息标签页卡片 */
.info-tabs-card {
  min-height: 600px;
}

/* 积分记录 */
.records-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.points-add {
  color: #67c23a;
  font-weight: bold;
}

.points-deduct {
  color: #f56c6c;
  font-weight: bold;
}

/* 学习情况 */
.study-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.study-stat-item {
  text-align: center;
}

.study-stat-item .stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.study-stat-item .stat-label {
  font-size: 14px;
  color: #606266;
}

/* 成绩颜色 */
.score-excellent { color: #67c23a; font-weight: bold; }
.score-good { color: #409eff; font-weight: bold; }
.score-average { color: #e6a23c; }
.score-pass { color: #909399; }
.score-fail { color: #f56c6c; font-weight: bold; }

/* 活动参与 */
.activities-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 32px;
  color: #409eff;
}

.stat-info .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-info .stat-label {
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .records-header .el-form {
    display: block;
  }

  .records-header .el-form-item {
    margin-bottom: 10px;
  }

  .activity-stat-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>

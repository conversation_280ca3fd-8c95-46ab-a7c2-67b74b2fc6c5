package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.pojo.OldStu;
import com.zhentao.service.OldStuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/oldStu")
public class OldStuController {

    @Autowired
    private OldStuService oldStuService;

    @PostMapping("/findPage")
    public Page<OldStu> findPage(@RequestBody OldStu oldStu) {
        Page<OldStu> page = new Page<>(oldStu.getPageNum(), oldStu.getPageSize());
        LambdaQueryWrapper<OldStu> wrapper = new LambdaQueryWrapper<>();

        // 基础条件：只查询未删除的记录
        wrapper.eq(OldStu::getDelFlag, 0);

        // 添加查询条件
        // 按学号模糊查询
        if (StringUtils.hasText(oldStu.getOldstuNo())) {
            wrapper.like(OldStu::getOldstuNo, oldStu.getOldstuNo());
        }

        // 按学生姓名模糊查询
        if (StringUtils.hasText(oldStu.getStuName())) {
            wrapper.like(OldStu::getStuName, oldStu.getStuName());
        }

        // 按班级名称模糊查询
        if (StringUtils.hasText(oldStu.getClassName())) {
            wrapper.like(OldStu::getClassName, oldStu.getClassName());
        }

        // 按性别精确查询
        if (oldStu.getGender()!=null) {
            wrapper.eq(OldStu::getGender, oldStu.getGender());
        }

        // 按状态精确查询
        if (oldStu.getStatus() != null) {
            wrapper.eq(OldStu::getStatus, oldStu.getStatus());
        }

        if (oldStu.getRole() != null) {
            wrapper.eq(OldStu::getRole, oldStu.getRole());
        }

        // 按创建时间倒序排序
        wrapper.orderByDesc(OldStu::getCreateTime);

        return oldStuService.page(page, wrapper);
    }

}

package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.zhentao.dto.system.system.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班级表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@TableName("edu_class")
public class EduClass extends Page  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 班级ID
     */
    @TableId(value = "class_id", type = IdType.AUTO)
    private Integer classId;

    @TableField(exist = false)
    private List<EduStudent> students;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 所属阶段ID
     */
    private Integer stageId;

    /**
     * 教室号
     */
    private String classroom;

    /**
     * 讲师ID
     */
    private Integer teacherId;

    /**
     * 导员ID
     */
    private Integer counselorId;

    /**
     * 阶段主任ID
     */
    private Integer directorId;

    /**
     * 开班日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 状态（0-正常，1-结束）
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建者ID
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者ID
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0-存在，1-删除）
     */
    private Integer delFlag;

}

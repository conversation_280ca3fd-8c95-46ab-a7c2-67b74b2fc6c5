# 表ID自增重置功能使用指南

## 功能概述

在批量导入学生数据前，系统可以自动重置表的ID自增值，确保新导入的数据从指定的ID开始。这个功能特别适用于：

- 清空数据后重新导入
- 确保ID从1开始的整洁性
- 避免ID跳跃过大的问题

## 配置方式

### 1. 自动重置（推荐）

系统会在每次批量导入前自动检查配置并重置相应表的ID自增值。

**配置文件位置**：`src/main/resources/auto-increment-config.properties`

```properties
# 总开关：是否启用自动重置功能
auto.increment.reset.enabled=true

# 学生表配置（默认启用）
auto.increment.reset.edu_student.enabled=true
auto.increment.reset.edu_student.start_value=1

# 班级表配置（默认禁用）
auto.increment.reset.edu_class.enabled=false
auto.increment.reset.edu_class.start_value=1

# 阶段表配置（默认禁用）
auto.increment.reset.edu_stage.enabled=false
auto.increment.reset.edu_stage.start_value=1

# 历史学生表配置（默认禁用）
auto.increment.reset.old_stu.enabled=false
auto.increment.reset.old_stu.start_value=1

# 失败处理策略：ignore(忽略), warn(警告), error(抛出异常)
auto.increment.reset.failure.strategy=warn

# 是否备份当前自增值
auto.increment.reset.backup.enabled=true
```

### 2. 手动重置

通过API端点手动触发重置操作。

#### 完整重置API
```http
POST /edu-student/reset-auto-increment
```

**参数**：
- `resetStudentTable`: 是否重置学生表（默认true）
- `resetClassTable`: 是否重置班级表（默认false）
- `resetStageTable`: 是否重置阶段表（默认false）
- `resetOldStuTable`: 是否重置历史学生表（默认false）

**示例**：
```javascript
// 只重置学生表
fetch('/edu-student/reset-auto-increment?resetStudentTable=true', {
  method: 'POST'
});

// 重置学生表和班级表
fetch('/edu-student/reset-auto-increment?resetStudentTable=true&resetClassTable=true', {
  method: 'POST'
});
```

#### 快速重置API
```http
POST /edu-student/reset-student-auto-increment
```

只重置学生表，无需参数，执行速度更快。

## 使用场景

### 场景1：常规批量导入
1. 配置文件中启用学生表重置：`auto.increment.reset.edu_student.enabled=true`
2. 执行批量导入操作
3. 系统自动重置学生表ID自增为1
4. 新导入的学生ID从1开始

### 场景2：完全重新开始
1. 配置文件中启用所有表重置：
   ```properties
   auto.increment.reset.edu_student.enabled=true
   auto.increment.reset.edu_class.enabled=true
   auto.increment.reset.edu_stage.enabled=true
   ```
2. 执行批量导入
3. 所有相关表的ID都从1开始

### 场景3：手动控制
1. 禁用自动重置：`auto.increment.reset.enabled=false`
2. 在需要时手动调用重置API
3. 灵活控制重置时机和范围

### 场景4：自定义起始值
1. 修改配置文件中的起始值：
   ```properties
   auto.increment.reset.edu_student.start_value=1000
   ```
2. 学生ID将从1000开始

## 配置选项详解

### 总开关
```properties
auto.increment.reset.enabled=true
```
- `true`: 启用自动重置功能
- `false`: 禁用自动重置功能

### 表级配置
```properties
auto.increment.reset.{表名}.enabled=true
auto.increment.reset.{表名}.start_value=1
```
- `enabled`: 是否重置该表
- `start_value`: 重置后的起始值

### 失败处理策略
```properties
auto.increment.reset.failure.strategy=warn
```
- `ignore`: 忽略重置失败，静默继续导入
- `warn`: 记录警告信息，继续导入
- `error`: 抛出异常，停止导入

### 备份功能
```properties
auto.increment.reset.backup.enabled=true
```
- `true`: 重置前备份当前自增值到日志
- `false`: 不备份

## 执行流程

### 自动重置流程
1. 批量导入开始
2. 检查配置文件中的总开关
3. 如果启用，读取各表的配置
4. 备份当前自增值（如果启用）
5. 执行表ID重置
6. 记录重置结果
7. 继续执行批量导入

### 手动重置流程
1. 调用重置API
2. 根据参数确定要重置的表
3. 执行重置操作
4. 返回重置结果

## 日志输出示例

### 成功重置
```
=== 表ID自增重置配置 ===
总开关: true
失败策略: warn
备份功能: true
日志级别: info
表配置:
  edu_student: 启用, 起始值: 1
  edu_class: 禁用, 起始值: 1
  edu_stage: 禁用, 起始值: 1
  old_stu: 禁用, 起始值: 1

开始批量重置表ID自增（使用配置）...
备份表 edu_student 当前自增值: 1523
表 edu_student 的ID自增已重置为 1
批量重置表ID自增完成 - 成功: 1, 失败: 0
```

### 重置失败
```
开始批量重置表ID自增（使用配置）...
重置表 edu_student ID自增失败: Table 'jifen.edu_student' doesn't exist
批量重置表ID自增完成 - 成功: 0, 失败: 1
```

## 注意事项

### 1. 数据安全
- 重置ID自增会影响新插入数据的ID值
- 建议在清空表数据后再重置ID自增
- 启用备份功能以记录原始自增值

### 2. 并发安全
- 重置操作在事务中执行，确保原子性
- 避免在高并发环境下频繁重置

### 3. 性能影响
- 重置操作本身很快，对导入性能影响微乎其微
- 备份功能会增加少量查询开销

### 4. 数据库兼容性
- 当前实现基于MySQL语法
- 其他数据库可能需要调整SQL语句

## 故障排查

### 问题1：重置失败
**现象**：日志显示重置失败
**可能原因**：
- 表不存在
- 数据库权限不足
- 表被锁定

**解决方案**：
- 检查表名是否正确
- 确认数据库用户权限
- 检查是否有其他操作锁定了表

### 问题2：配置不生效
**现象**：配置了重置但没有执行
**可能原因**：
- 总开关未启用
- 表级开关未启用
- 配置文件路径错误

**解决方案**：
- 检查 `auto.increment.reset.enabled=true`
- 检查具体表的 `enabled=true`
- 确认配置文件在classpath中

### 问题3：起始值不正确
**现象**：重置后ID不是期望的值
**可能原因**：
- 配置的起始值不正确
- 表中仍有数据

**解决方案**：
- 检查配置文件中的 `start_value`
- 清空表数据后再重置

## 最佳实践

1. **开发环境**：启用所有表重置，便于测试
2. **生产环境**：谨慎使用，建议只重置必要的表
3. **备份策略**：始终启用备份功能
4. **监控日志**：关注重置操作的日志输出
5. **测试验证**：重置后验证ID是否符合预期

通过合理配置和使用这个功能，您可以确保批量导入的数据具有整洁、连续的ID序列。

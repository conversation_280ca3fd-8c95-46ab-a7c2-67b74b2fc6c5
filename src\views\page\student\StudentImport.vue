<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { getOldStuList } from '@/api/system/oldStu.js'

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

// 查询表单
const queryForm = reactive({
  oldstuNo: '',
  stuName: '',
  className: '',
  gender: '',
  status: null,
  pageNum: 1,
  pageSize: 10
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const res = await getOldStuList(queryForm)
    if (res.status === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (error) {
    console.error('获取归档数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = () => {
  queryForm.pageNum = 1
  fetchData()
}

// 重置查询条件
const resetQuery = () => {
  queryForm.oldstuNo = ''
  queryForm.stuName = ''
  queryForm.className = ''
  queryForm.gender = ''
  queryForm.status = null
  queryForm.pageNum = 1
  fetchData()
}

// 刷新
const refreshTable = () => {
  fetchData()
}

// 分页处理
const handleCurrentChange = (val) => {
  queryForm.pageNum = val
  fetchData()
}

const handleSizeChange = (val) => {
  queryForm.pageSize = val
  queryForm.pageNum = 1
  fetchData()
}

// 获取积分样式
const getPointsClass = (points) => {
  if (!points) return ''
  if (points >= 90) return 'points-excellent'
  if (points >= 80) return 'points-good'
  if (points >= 60) return 'points-pass'
  return 'points-fail'
}

onMounted(fetchData)
</script>

<template>
  <div class="archive-records-container">
    <!-- 查询条件 -->
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>归档记录查询</h3>
          <div class="header-actions">
            <span class="total-info">共 {{ total }} 条记录</span>
          </div>
        </div>
      </template>

      <el-form :inline="true" class="filter-form">
        <el-form-item label="学号">
          <el-input
            v-model="queryForm.oldstuNo"
            placeholder="请输入学号"
            clearable
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="姓名">
          <el-input
            v-model="queryForm.stuName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="班级">
          <el-input
            v-model="queryForm.className"
            placeholder="请输入班级名称"
            clearable
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="性别">
          <el-select v-model="queryForm.gender" placeholder="请选择性别" clearable style="width: 120px">
            <el-option label="男" value="1" />
            <el-option label="女" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="正常" :value="0" />
            <el-option label="休学" :value="1" />
            <el-option label="退学" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="refreshTable" :icon="Refresh">刷新</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>归档学生信息（只读）</h3>
        </div>
      </template>

      <el-table :data="tableData" v-loading="loading" border stripe style="width: 100%">
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="oldstuNo" label="学号" width="120" sortable />
        <el-table-column prop="stuName" label="学生姓名" width="100" />
        <el-table-column label="性别" width="60">
          <template #default="scope">
            <span v-if="scope.row.gender === '1' || scope.row.gender === 1">男</span>
            <span v-else-if="scope.row.gender === '0' || scope.row.gender === 0">女</span>
            <span v-else>未知</span>
          </template>
        </el-table-column>
        <el-table-column prop="className" label="班级" width="120" />
        <el-table-column prop="points" label="积分" width="80" sortable>
          <template #default="scope">
            <span :class="getPointsClass(scope.row.points)">{{ scope.row.points }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 0" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status === 1" type="warning">休学</el-tag>
            <el-tag v-else-if="scope.row.status === 2" type="danger">退学</el-tag>
            <el-tag v-else>未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="归档时间" width="180" sortable />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.pageNum"
          v-model:page-size="queryForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.archive-records-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background: #f8fafc;
}

.archive-records-container::-webkit-scrollbar {
  display: none;
}

.filter-card, .table-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: visible;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: white;
  border: 1px solid #e5e7eb;
  animation: slideInUp 0.5s ease-out;
}

.filter-card:hover, .table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0 16px 0;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  font-size: 18px;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.total-info {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.points-excellent {
  color: #10b981;
  font-weight: 600;
}

.points-good {
  color: #3b82f6;
  font-weight: 600;
}

.points-pass {
  color: #f59e0b;
  font-weight: 500;
}

.points-fail {
  color: #ef4444;
  font-weight: 600;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

:deep(.el-table th) {
  background: #f9fafb;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafafa;
}

:deep(.el-table__body tr:hover > td) {
  background: #f0f9ff !important;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  font-size: 12px;
}

:deep(.el-tag--success) {
  background: #dcfce7;
  color: #166534;
}

:deep(.el-tag--warning) {
  background: #fef3c7;
  color: #92400e;
}

:deep(.el-tag--danger) {
  background: #fee2e2;
  color: #991b1b;
}

/* 表单组件样式优化 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #d1d5db;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .archive-records-container {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
    gap: 12px;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .filter-card, .table-card {
    border-radius: 8px;
    margin-bottom: 16px;
  }
}
</style>
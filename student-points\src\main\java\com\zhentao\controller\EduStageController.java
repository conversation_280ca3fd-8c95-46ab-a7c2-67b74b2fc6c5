package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.zhentao.pojo.EduStage;
import com.zhentao.service.EduStageService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 专业表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/edu-stage")
public class EduStageController {
    @Autowired
    private EduStageService eduStageService;

    /**
     * 获取所有专业信息
     * @return 专业列表
     */
    @GetMapping("/getAllStages")
    public Result getAllStages() {
        try {
            List<EduStage> stages = eduStageService.list();
            return Result.OK(stages);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取专业信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有专业信息（列表形式）
     * @return 专业列表
     */
    @GetMapping("/list")
    public Result list() {
        try {
            List<EduStage> stages = eduStageService.list();
            return Result.OK(stages);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取专业信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取可选的专业信息（排除父级专业，如"实训阶段"等）
     * @return 可选专业列表
     */
    @GetMapping("/getSelectableStages")
    public Result getSelectableStages() {
        try {
            // 获取所有阶段
            List<EduStage> allStages = eduStageService.list();
            return Result.OK(allStages);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取可选专业信息失败：" + e.getMessage());
        }
    }

    /**
     * 调试用：获取指定专业名称的阶段ID
     * 用于帮助诊断专业筛选的问题
     * @param stageName 专业名称
     * @return 匹配的阶段信息
     */
    @GetMapping("/debugMatchStage")
    public Result debugMatchStage(String stageName) {
        try {
            if (StringUtils.isBlank(stageName)) {
                return Result.ERROR("请提供专业名称");
            }
            
            String searchStageName = stageName.trim();
            List<EduStage> allStages = eduStageService.list();
            
            Map<String, Object> result = new HashMap<>();
            result.put("searchTerm", searchStageName);
            
            // 精确匹配（区分大小写）
            List<EduStage> exactMatches = allStages.stream()
                .filter(stage -> stage.getStageName().equals(searchStageName))
                .collect(Collectors.toList());
            result.put("exactMatches", exactMatches);
            
            // 不区分大小写匹配
            List<EduStage> caseInsensitiveMatches = allStages.stream()
                .filter(stage -> stage.getStageName().toLowerCase().equals(searchStageName.toLowerCase()))
                .collect(Collectors.toList());
            result.put("caseInsensitiveMatches", caseInsensitiveMatches);
            
            // 包含匹配
            List<EduStage> containsMatches = allStages.stream()
                .filter(stage -> stage.getStageName().contains(searchStageName) || 
                       searchStageName.contains(stage.getStageName()))
                .collect(Collectors.toList());
            result.put("containsMatches", containsMatches);
            
            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("诊断专业匹配失败：" + e.getMessage());
        }
    }
}

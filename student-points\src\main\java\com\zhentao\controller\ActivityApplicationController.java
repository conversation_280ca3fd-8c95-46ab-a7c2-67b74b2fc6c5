package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.dto.system.system.ApplicationDto;
import com.zhentao.dto.system.system.PageActivityApplicationDto;
import com.zhentao.pojo.Activity;
import com.zhentao.pojo.ActivityApplication;
import com.zhentao.pojo.EduStudent;
import com.zhentao.service.ActivityApplicationService;
import com.zhentao.service.ActivityService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.SysUserService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 活动表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@RestController
@RequestMapping("/activityApplication")
public class ActivityApplicationController {
    @Autowired
    private ActivityApplicationService activityApplicationService;
    @Autowired
    private EduStudentService eduStudentService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private SysUserService sysUserService;
    /**
     * 报名活动
     */
    @PostMapping("/signUpActivity")
    public Result signUpActivity(@RequestBody ApplicationDto applicationDto) {
        return activityApplicationService.signUpActivity(applicationDto);
    }
    /**
     * 取消报名活动
     */
    @PostMapping("/cancelSignUpActivity")
    public Result cancelSignUpActivity(@RequestParam Integer applicationId, @RequestParam Integer activityId) {
        return activityApplicationService.cancelSignUpActivity(applicationId, activityId);
    }
    /**
     * 活动报名列表
     */
    @PostMapping("/activityApplicationPage")
    public Page<ActivityApplication> activityApplicationPage(@RequestBody PageActivityApplicationDto pageActivityApplicationDto) {
        System.err.println(pageActivityApplicationDto.toString());
        return activityApplicationService.activityApplicationPage(pageActivityApplicationDto);
    }
    /**
     * 查询所有活动
     */
    @PostMapping("/findActivity")
    public Result findActivity() {
        return activityApplicationService.findActivity();
    }

    /**
     * 查询某个学生参加的活动
     */
    @PostMapping("/findStudentActivity")
    public Result findStudentActivity(@RequestBody ApplicationDto dto) {
        Page<ActivityApplication> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        QueryWrapper<ActivityApplication> wrapper = new QueryWrapper<>();
        wrapper.eq("name", eduStudentService.getById(dto.getStudentNo()).getRealName());
        Page<ActivityApplication> page1 = activityApplicationService.page(page, wrapper);
        for (ActivityApplication record : page1.getRecords()) {
            Activity byId = activityService.getById(record.getActivityId());
            byId.setCreateUser(sysUserService.getById(byId.getCreateBy()));
            record.setActivity(byId);
        }
        return Result.OK(page1);
    }

}

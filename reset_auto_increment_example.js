/**
 * 表ID自增重置功能前端调用示例
 * 提供各种重置场景的JavaScript调用方法
 */

// API基础配置
const API_BASE_URL = '/edu-student';
const getAuthHeaders = () => ({
  'Authorization': localStorage.getItem('Authorization'),
  'Content-Type': 'application/json'
});

/**
 * 快速重置学生表ID自增
 * 最简单的重置方式，只重置edu_student表
 */
const quickResetStudentAutoIncrement = async () => {
  try {
    console.log('开始快速重置学生表ID自增...');
    
    const response = await fetch(`${API_BASE_URL}/reset-student-auto-increment`, {
      method: 'POST',
      headers: getAuthHeaders()
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('✅ 学生表ID自增重置成功');
      ElMessage.success('学生表ID自增重置成功');
    } else {
      console.error('❌ 重置失败:', result.message);
      ElMessage.error('重置失败: ' + result.message);
    }
    
    return result;
  } catch (error) {
    console.error('❌ 重置请求失败:', error);
    ElMessage.error('重置请求失败: ' + error.message);
    throw error;
  }
};

/**
 * 自定义重置多个表的ID自增
 * @param {Object} options 重置选项
 * @param {boolean} options.resetStudentTable 是否重置学生表
 * @param {boolean} options.resetClassTable 是否重置班级表
 * @param {boolean} options.resetStageTable 是否重置阶段表
 * @param {boolean} options.resetOldStuTable 是否重置历史学生表
 */
const customResetAutoIncrement = async (options = {}) => {
  const {
    resetStudentTable = true,
    resetClassTable = false,
    resetStageTable = false,
    resetOldStuTable = false
  } = options;
  
  try {
    console.log('开始自定义重置表ID自增...', options);
    
    const params = new URLSearchParams({
      resetStudentTable: resetStudentTable.toString(),
      resetClassTable: resetClassTable.toString(),
      resetStageTable: resetStageTable.toString(),
      resetOldStuTable: resetOldStuTable.toString()
    });
    
    const response = await fetch(`${API_BASE_URL}/reset-auto-increment?${params}`, {
      method: 'POST',
      headers: getAuthHeaders()
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('✅ 表ID自增重置成功');
      ElMessage.success('表ID自增重置成功');
    } else {
      console.error('❌ 重置失败:', result.message);
      ElMessage.error('重置失败: ' + result.message);
    }
    
    return result;
  } catch (error) {
    console.error('❌ 重置请求失败:', error);
    ElMessage.error('重置请求失败: ' + error.message);
    throw error;
  }
};

/**
 * 重置所有表的ID自增
 * 用于完全重新开始的场景
 */
const resetAllTablesAutoIncrement = async () => {
  return await customResetAutoIncrement({
    resetStudentTable: true,
    resetClassTable: true,
    resetStageTable: true,
    resetOldStuTable: true
  });
};

/**
 * 重置学生和班级表的ID自增
 * 用于重新导入学生和班级数据的场景
 */
const resetStudentAndClassAutoIncrement = async () => {
  return await customResetAutoIncrement({
    resetStudentTable: true,
    resetClassTable: true,
    resetStageTable: false,
    resetOldStuTable: false
  });
};

/**
 * 带确认对话框的重置操作
 * @param {string} resetType 重置类型：'quick', 'all', 'student-class', 'custom'
 * @param {Object} customOptions 自定义选项（当resetType为'custom'时使用）
 */
const resetWithConfirmation = async (resetType = 'quick', customOptions = {}) => {
  let confirmMessage = '';
  let resetFunction = null;
  
  switch (resetType) {
    case 'quick':
      confirmMessage = '确定要重置学生表的ID自增吗？新导入的学生ID将从1开始。';
      resetFunction = quickResetStudentAutoIncrement;
      break;
    case 'all':
      confirmMessage = '确定要重置所有表的ID自增吗？这将影响学生、班级、阶段和历史数据表。';
      resetFunction = resetAllTablesAutoIncrement;
      break;
    case 'student-class':
      confirmMessage = '确定要重置学生表和班级表的ID自增吗？';
      resetFunction = resetStudentAndClassAutoIncrement;
      break;
    case 'custom':
      confirmMessage = '确定要执行自定义的ID自增重置吗？';
      resetFunction = () => customResetAutoIncrement(customOptions);
      break;
    default:
      throw new Error('不支持的重置类型: ' + resetType);
  }
  
  try {
    await ElMessageBox.confirm(
      confirmMessage,
      '重置表ID自增确认',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning',
        distinguishCancelAndClose: true,
        confirmButtonClass: 'el-button--warning'
      }
    );
    
    return await resetFunction();
  } catch (action) {
    if (action === 'cancel') {
      ElMessage.info('已取消重置操作');
    }
    return null;
  }
};

/**
 * 批量导入前的重置操作
 * 在执行批量导入前调用，确保ID从1开始
 */
const resetBeforeBatchImport = async () => {
  try {
    console.log('批量导入前重置ID自增...');
    
    // 显示加载提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在重置表ID自增...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    try {
      // 执行快速重置（只重置学生表）
      await quickResetStudentAutoIncrement();
      
      // 短暂延迟，确保重置完成
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log('✅ 批量导入前重置完成，可以开始导入');
      return true;
    } finally {
      loading.close();
    }
  } catch (error) {
    console.error('❌ 批量导入前重置失败:', error);
    ElMessage.error('重置失败，建议稍后重试');
    return false;
  }
};

/**
 * 检查重置状态
 * 查询当前表的自增值状态
 */
const checkAutoIncrementStatus = async () => {
  try {
    // 这里可以添加一个查询当前自增值的API
    console.log('检查表ID自增状态...');
    
    // 示例：查询学生表的当前自增值
    const response = await fetch(`${API_BASE_URL}/check-auto-increment-status`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('当前表ID自增状态:', result);
      return result;
    } else {
      console.log('无法获取自增状态，可能API不存在');
      return null;
    }
  } catch (error) {
    console.error('检查自增状态失败:', error);
    return null;
  }
};

/**
 * Vue组件中的使用示例
 */
const vueComponentExample = {
  data() {
    return {
      resetLoading: false
    };
  },
  methods: {
    // 在批量导入按钮点击时调用
    async handleBatchImport() {
      try {
        this.resetLoading = true;
        
        // 1. 重置ID自增
        const resetSuccess = await resetBeforeBatchImport();
        if (!resetSuccess) {
          return;
        }
        
        // 2. 执行批量导入
        await this.executeBatchImport();
        
      } catch (error) {
        console.error('批量导入失败:', error);
        ElMessage.error('批量导入失败');
      } finally {
        this.resetLoading = false;
      }
    },
    
    // 手动重置按钮
    async handleManualReset() {
      await resetWithConfirmation('quick');
    },
    
    // 完全重置按钮
    async handleFullReset() {
      await resetWithConfirmation('all');
    }
  }
};

// 导出函数供全局使用
if (typeof window !== 'undefined') {
  window.quickResetStudentAutoIncrement = quickResetStudentAutoIncrement;
  window.customResetAutoIncrement = customResetAutoIncrement;
  window.resetAllTablesAutoIncrement = resetAllTablesAutoIncrement;
  window.resetWithConfirmation = resetWithConfirmation;
  window.resetBeforeBatchImport = resetBeforeBatchImport;
  window.checkAutoIncrementStatus = checkAutoIncrementStatus;
  
  console.log('表ID自增重置工具已加载！');
  console.log('使用方法:');
  console.log('- quickResetStudentAutoIncrement(): 快速重置学生表');
  console.log('- resetAllTablesAutoIncrement(): 重置所有表');
  console.log('- resetWithConfirmation("quick"): 带确认的重置');
  console.log('- resetBeforeBatchImport(): 批量导入前重置');
}

// Node.js 环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    quickResetStudentAutoIncrement,
    customResetAutoIncrement,
    resetAllTablesAutoIncrement,
    resetWithConfirmation,
    resetBeforeBatchImport,
    checkAutoIncrementStatus
  };
}

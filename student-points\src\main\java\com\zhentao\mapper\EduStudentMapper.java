package com.zhentao.mapper;

import com.zhentao.pojo.EduStudent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 学生表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Mapper
public interface EduStudentMapper extends BaseMapper<EduStudent> {

    @Insert("insert into 'edustudent' (null,student_no,real_name,gender,phone,email,class_id,points,status,create_by,create_time,update_by,update_time,remark,del_flag) " +
            "values(null,#{student_no},#{real_name},#{gender},#{phone},#{email},#{class_id},#{points},#{status},#{create_by},#{create_time},#{update_by},#{update_time},#{remark},#{del_flag})")
    void add(EduStudent eduStudent);

    @Select("delete from edustudent where id=#{id}")
    void del(Integer id);

    @Select("update edustudent set #{student_no},#{real_name},#{gender},#{phone},#{email},#{class_id},#{points},#{status},#{create_by},#{create_time},#{update_by},#{update_time},#{remark},#{del_flag} where id=#{id}")
    void update(EduStudent eduStudent);
    @Select("select * from edustudent where name like ('%',#{name},'%')")
    List<EduStudent> listname();
    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "WHERE s.student_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.real_name LIKE CONCAT('%', #{keyword}, '%') " +
            "LIMIT 10")
    List<EduStudent> searchStudents(String keyword);

    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "LIMIT 10")
    List<EduStudent> searchStudentsLimited();

    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "WHERE s.class_id = #{classId} " +
            "AND (s.student_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.real_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "LIMIT 10")
    List<EduStudent> searchStudentsByClass(@Param("keyword") String keyword, @Param("classId") Integer classId);

    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "WHERE (c.teacher_id = #{teacherId} OR c.counselor_id = #{teacherId}) " +
            "AND (s.student_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.real_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "LIMIT 10")
    List<EduStudent> searchStudentsForTeacher(@Param("keyword") String keyword, @Param("teacherId") Integer teacherId);
}

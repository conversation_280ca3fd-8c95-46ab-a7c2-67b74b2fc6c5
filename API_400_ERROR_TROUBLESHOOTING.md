# API 400错误排查指南

## 问题现象
所有后端API都返回400 Bad Request错误

## 可能原因及解决方案

### 1. 编译错误
**检查方法**：
```bash
cd student-points
mvn compile
```

**常见编译错误**：
- 缺少依赖
- 语法错误
- 导入错误

**解决方案**：
- 修复编译错误
- 重新编译项目

### 2. Spring Boot启动失败
**检查方法**：
- 查看控制台启动日志
- 检查是否有异常堆栈

**常见问题**：
- 配置文件错误
- Bean循环依赖
- 端口被占用

**解决方案**：
```bash
# 重启应用
mvn spring-boot:run

# 或者
java -jar target/student-points-*.jar
```

### 3. 数据库连接问题
**检查方法**：
```yaml
# 检查 application.yml 中的数据库配置
spring:
  datasource:
    url: jdbc:mysql://...
    username: xxx
    password: xxx
```

**解决方案**：
- 确认数据库服务运行
- 验证连接参数
- 测试数据库连接

### 4. 请求格式问题
**检查方法**：
- 验证Content-Type头
- 检查请求体格式
- 确认参数类型

**解决方案**：
```javascript
// 确保请求头正确
headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token'
}
```

### 5. 认证问题
**检查方法**：
- 验证Authorization token
- 检查登录状态

**解决方案**：
- 重新登录获取token
- 检查token格式和有效期

## 排查步骤

### 第一步：检查应用状态
1. **确认应用启动**：
   - 检查控制台是否显示"Started StudentPointsApplication"
   - 确认端口8080是否被监听

2. **查看启动日志**：
   ```
   查找错误信息：
   - ERROR级别日志
   - 异常堆栈信息
   - Bean创建失败
   ```

### 第二步：测试基础连接
1. **使用测试工具**：
   - 打开 `test_api_status.html`
   - 执行基础API测试
   - 查看具体错误信息

2. **手动测试**：
   ```bash
   # 测试应用是否响应
   curl -X GET http://localhost:8080/edu-student/getClassOptions
   ```

### 第三步：检查具体错误
1. **查看浏览器Network面板**：
   - 检查请求详情
   - 查看响应内容
   - 确认状态码

2. **查看后端日志**：
   - 检查控制台输出
   - 查找异常信息
   - 确认请求是否到达后端

### 第四步：逐步修复
1. **修复编译错误**：
   ```bash
   mvn clean compile
   ```

2. **重启应用**：
   ```bash
   mvn spring-boot:run
   ```

3. **验证修复**：
   - 重新测试API
   - 确认功能正常

## 常见解决方案

### 解决方案1：重新编译
```bash
cd student-points
mvn clean
mvn compile
mvn spring-boot:run
```

### 解决方案2：检查依赖
```xml
<!-- 确保pom.xml中有必要的依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-jdbc</artifactId>
</dependency>
```

### 解决方案3：简化配置
```java
// 移除复杂的配置类
// 使用最基本的实现
```

### 解决方案4：数据库检查
```sql
-- 测试数据库连接
SELECT 1;

-- 检查表是否存在
SHOW TABLES LIKE 'edu_student';
```

## 应急处理

如果问题持续存在，可以：

1. **回滚到工作版本**：
   ```bash
   git checkout HEAD~1
   mvn spring-boot:run
   ```

2. **使用最小配置**：
   - 注释掉新添加的代码
   - 只保留核心功能
   - 逐步添加功能

3. **重新创建项目**：
   - 备份重要代码
   - 重新生成Spring Boot项目
   - 逐步迁移功能

## 预防措施

1. **代码提交前测试**：
   - 本地编译通过
   - 基础功能测试
   - API响应正常

2. **分步骤开发**：
   - 小步快跑
   - 及时测试
   - 避免大量修改

3. **保持备份**：
   - 定期提交代码
   - 保留工作版本
   - 记录修改内容

## 联系支持

如果以上方法都无法解决问题，请提供：

1. **完整的错误日志**
2. **应用启动日志**
3. **浏览器Network面板截图**
4. **最近的代码修改记录**

这样可以更快速地定位和解决问题。

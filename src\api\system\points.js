import Request from "@/request/index.js";

export function findAll<PERSON><PERSON>(data){
    return Request({
        url: '/integral-rules/findAllJia',
        method: 'post',
        data:data
    })
}
export function findAllJian(data){
    return Request({
        url: '/integral-rules/findAllJian',
        method: 'post',
        data:data
    })
}
export function addRules(data){
    return Request({
        url: '/integral-rules/addRules',
        method: 'post',
        data:data
    })
}
export function updateRules(data){
    return Request({
        url: '/integral-rules/updateRules',
        method: 'post',
        data:data
    })
}
export function delRules(id){
    return Request({
        url: '/integral-rules/delRules?id='+id,
        method: 'post'
    })
}
export function findById(id){
    return Request({
        url: '/integral-rules/findById?id='+id,
        method: 'post'
    })
}
const roleId=localStorage.getItem('roleId');
let status=null;
let status1=null;
let status2=null;
if(roleId.includes(6)){
    status=1;
    status1=1;
    status2=1;
}else if(roleId.includes(5)||roleId.includes(4)){
    status=2;
    status1=1;
    status2=1;
}else if(roleId.includes(3)||roleId.includes(2)){
    status=2;
    status1=2;
    status2=1;
}else if(roleId.includes(1)){
    status=2;
    status1=2;
    status2=1;
}
export function addJiFen(data){
    console.log('Sending data to backend:', data);
    // 确保数据格式符合后端要求
    const requestData = {
        studentId: data.studentId || 0,
        classId: data.classId || 0,
        pointsChange: 1, // 1为加分，2为减分
        points: data.points || 0,
        reason: data.reason || '',
        img: data.img || '', // MinIO上传后的图片URL，多个URL用逗号分隔
        status: status, // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
        status1: status1, // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
        status2: status2 // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
    };

    console.log('Formatted request data:', requestData);

    // Use the real backend API
    return Request({
        url: '/points-apply/addJiFen',
        method: 'post',
        data: requestData
    })
}

export function searchStudents(keyword){
    // Use the real backend API instead of mock data
    return Request({
        url: '/edu-student/search',
        method: 'get',
        params: { keyword }
    })
}

export function getPointsRecords(data){
    console.log('Sending query to backend:', data);

    // Ensure data is an object
    const queryParams = data || {};

    return Request({
        url: '/points-apply/list',
        method: 'post',
        data: queryParams
    })
}

export function deductPoints(data){
    console.log('Sending deduction data to backend:', data);

    // 确保数据格式符合后端要求
    const requestData = {
        studentId: data.studentId || 0,
        classId: data.classId || 0,
        pointsChange: 2, // 2为减分
        points: data.points || 0,
        reason: data.reason || '',
        img: data.img || '', // MinIO上传后的图片URL，多个URL用逗号分隔
        status: 1 // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
    };

    console.log('Formatted deduction request data:', requestData);

    // Use the same API endpoint as addJiFen but with pointsChange=2
    return Request({
        url: '/points-apply/addJiFen',
        method: 'post',
        data: requestData
    })
}

/**
 * 获取积分历史记录
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getPointsHistory(params) {
    // 确保params是一个对象
    const queryParams = params || {};

    // 确保分页参数存在
    if (!queryParams.pageNum) {
        queryParams.pageNum = 1;
    }
    if (!queryParams.pageSize) {
        queryParams.pageSize = 10;
    }

    console.log('发送历史记录查询参数:', queryParams);

    return Request({
        url: '/points-apply/history',
        method: 'post',
        data: queryParams
    })
}

/**
 * 获取积分统计信息
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getPointsStatistics(params) {
    console.log('Sending statistics query to backend:', params);

    // 确保params是一个对象
    const queryParams = params || {};

    // 处理日期范围
    if (queryParams.startTime && typeof queryParams.startTime === 'object') {
        queryParams.startTime = formatDate(queryParams.startTime);
    }

    if (queryParams.endTime && typeof queryParams.endTime === 'object') {
        queryParams.endTime = formatDate(queryParams.endTime);
    }

    return Request({
        url: '/points-apply/statistics',
        method: 'post',
        data: queryParams
    })
}

/**
 * 格式化日期为字符串 (YYYY-MM-DD)
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    if (!date) return '';
    if (typeof date === 'string') return date;

    try {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    } catch (e) {
        console.error('Date formatting error:', e);
        return '';
    }
}

/**
 * 撤销积分申请
 * @param {Number} applyId 申请ID
 * @returns {Promise} 请求Promise
 */
export function cancelPointsApplication(applyId) {
    console.log('Canceling points application:', applyId);

    return Request({
        url: '/points-apply/cancel',
        method: 'post',
        data: { applyId }
    })
}

//---------------------------------------虚线一下全是cmy写的----------------------------------------------------------
/**
 * 查询当天所有加分的学生
 */
export function queryTodayAddPoints() {
  return Request({
    url: '/points-apply/queryTodayAddPoints',
    method: 'post'
  });
}

/**
 * 查询当天所有减分的学生
 */
export function queryTodayMinusPoints() {
  return Request({
    url: '/points-apply/queryTodayMinusPoints',
    method: 'post'
  });
}
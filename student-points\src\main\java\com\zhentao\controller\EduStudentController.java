package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.dto.BatchImportDto;
import com.zhentao.dto.BatchImportResultDto;
import com.zhentao.pojo.*;
import com.zhentao.service.*;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.HashSet;

/**
 * <p>
 * 学生表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/edu-student")
public class EduStudentController {
    @Autowired
    private EduStudentService eduStudentService;
    @Autowired
    private EduClassService eduClassService;
    @Autowired
    private EduStageService eduStageService;
    @Autowired
    private OldStuService oldStuService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SysUserRoleService sysUserRoleService;

    // 归档方法 - 优化版本
    @PostMapping("/archive")
    @Transactional(rollbackFor = Exception.class)
    public String archive(){
        try {
            // 1. 获取所有学生数据
            List<EduStudent> studentList = eduStudentService.list();

            if (studentList.isEmpty()) {
                return "没有数据需要归档";
            }

            // 2. 清空历史表
            oldStuService.remove(null);
            jdbcTemplate.execute("TRUNCATE TABLE old_stu");

            // 3. 批量获取班级信息，避免N+1查询问题
            Set<Integer> classIds = studentList.stream()
                    .map(EduStudent::getClassId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Integer, String> classNameMap = new HashMap<>();
            if (!classIds.isEmpty()) {
                List<EduClass> classList = eduClassService.listByIds(classIds);
                classNameMap = classList.stream()
                        .collect(Collectors.toMap(EduClass::getClassId, EduClass::getClassName));
            }

            // 4. 批量转换数据
            Date currentTime = new Date();
            List<OldStu> oldStuList = new ArrayList<>();

            for (EduStudent eduStudent : studentList) {
                OldStu oldStu = new OldStu();
                oldStu.setOldstuNo(eduStudent.getStudentNo());
                oldStu.setStuName(eduStudent.getRealName());
                oldStu.setGender(eduStudent.getGender());
                oldStu.setPhone(eduStudent.getPhone());
                oldStu.setEmail(eduStudent.getEmail());
                // 使用预加载的班级名称，避免重复查询
                oldStu.setClassName(classNameMap.getOrDefault(eduStudent.getClassId(), "未知班级"));
                oldStu.setPoints(eduStudent.getPoints());
                oldStu.setRemark(eduStudent.getRemark());
                oldStu.setStatus(eduStudent.getStatus());
                oldStu.setCreateTime(currentTime);
                oldStu.setDelFlag(0);
                oldStu.setPassword(eduStudent.getPassword());
                oldStu.setRole(eduStudent.getRole());

                oldStuList.add(oldStu);
            }

            // 5. 批量插入历史数据
            boolean success = oldStuService.saveBatch(oldStuList);

            if (success) {
                return "添加成功";
            } else {
                throw new RuntimeException("批量插入历史数据失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("归档操作失败: " + e.getMessage());
        }
    }

    // 清空所有数据
    @PostMapping("/clear")
    public String clear(){
        boolean remove = eduStudentService.remove(null);
        return remove ? "清空成功" : "清空失败";
    }



    @PostMapping("/findPage") //分页条件查询
    public Page<EduStudent> findPage(@RequestBody EduStudent student){
        try {
            // 创建分页对象
            Page<EduStudent> page = new Page<>(student.getPageNum(), student.getPageSize());
            
            // 添加调试日志
            System.out.println("分页查询参数: pageNum=" + student.getPageNum() + ", pageSize=" + student.getPageSize());

            // 获取当前登录用户id
            Integer currentUserId = UserContext.getCurrentUserId();
            // 获取当前用户对应的班级
            ArrayList<Integer> ids = new ArrayList<>();
            QueryWrapper<EduClass> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("teacher_id",currentUserId).or().eq("counselor_id",currentUserId);
            List<EduClass> list = eduClassService.list(queryWrapper);
            for (EduClass eduClass : list) {
                ids.add(eduClass.getClassId());
            }

            // 获取用户的角色
            ArrayList<Integer> roleId = new ArrayList<>();
            List<SysUserRole> list1 = sysUserRoleService.list(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, currentUserId));
            for (SysUserRole sysUserRole : list1) {
                roleId.add(sysUserRole.getRoleId());
            }

            // 1. 基本查询条件
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(StringUtils.isNotBlank(student.getRealName()), EduStudent::getRealName, student.getRealName());
            wrapper.eq(StringUtils.isNotBlank(student.getStudentNo()), EduStudent::getStudentNo, student.getStudentNo());
            wrapper.eq(student.getGender() != null, EduStudent::getGender, student.getGender());
            wrapper.in(!roleId.contains(1),EduStudent::getClassId,ids);
            
            // 2. 班级ID查询
            if (student.getClassId() != null) {
                wrapper.eq(EduStudent::getClassId, student.getClassId());
            }
            
            // 3. 如果只有专业条件但没有班级条件，需要先获取对应专业的所有班级ID
            if (StringUtils.isNotBlank(student.getStageName()) && student.getClassId() == null) {
                String stageName = student.getStageName().trim();
                
                // 3.1 获取所有阶段
                List<EduStage> allStages = eduStageService.list();
                
                // 3.2 查找匹配的阶段ID
                List<Integer> matchingStageIds = new ArrayList<>();
                for (EduStage stage : allStages) {
                    if (stage.getStageName() != null && 
                        stage.getStageName().trim().equalsIgnoreCase(stageName)) {
                        matchingStageIds.add(stage.getStageId());
                    }
                }
                
                if (!matchingStageIds.isEmpty()) {
                    
                    // 3.3 查找这些阶段对应的班级
                    List<EduClass> classes = eduClassService.list(
                        new LambdaQueryWrapper<EduClass>()
                            .in(EduClass::getStageId, matchingStageIds)
                    );
                    
                    // 3.4 获取班级ID列表
                    List<Integer> classIds = classes.stream()
                        .map(EduClass::getClassId)
                        .collect(Collectors.toList());
                    
                    if (!classIds.isEmpty()) {
                        wrapper.in(EduStudent::getClassId, classIds);
                    } else {
                        // 如果没有找到任何班级，返回空结果
                        Page<EduStudent> emptyPage = new Page<>();
                        emptyPage.setRecords(new ArrayList<>());
                        emptyPage.setTotal(0);
                        return emptyPage;
                    }
                } else {
                    // 如果没有找到匹配的专业，返回空结果
                    Page<EduStudent> emptyPage = new Page<>();
                    emptyPage.setRecords(new ArrayList<>());
                    emptyPage.setTotal(0);
                    return emptyPage;
                }
            }
            
            // 4. 添加稳定的排序，确保分页结果一致
            wrapper.orderByDesc(EduStudent::getCreateTime)
                   .orderByAsc(EduStudent::getStudentId); // 添加主键排序确保稳定性

            // 5. 执行查询
            Page<EduStudent> resultPage = eduStudentService.page(page, wrapper);
            
            // 添加调试日志
            System.out.println("查询结果: total=" + resultPage.getTotal() + ", records=" + (resultPage.getRecords() != null ? resultPage.getRecords().size() : 0));

            // 6. 填充班级名称和专业名称，并去重
            if (resultPage.getRecords() != null) {
                // 使用Set去重，基于studentId
                Set<Integer> seenIds = new HashSet<>();
                List<EduStudent> uniqueRecords = new ArrayList<>();

                for (EduStudent record : resultPage.getRecords()) {
                    if (!seenIds.contains(record.getStudentId())) {
                        seenIds.add(record.getStudentId());

                        EduClass eduClass = eduClassService.getById(record.getClassId());
                        if (eduClass != null) {
                            record.setClassName(eduClass.getClassName());

                            EduStage eduStage = eduStageService.getById(eduClass.getStageId());
                            if (eduStage != null) {
                                record.setStageName(eduStage.getStageName().trim());
                            }
                        }

                        uniqueRecords.add(record);
                    } else {
                        System.out.println("发现重复数据，studentId: " + record.getStudentId());
                    }
                }

                // 更新结果
                resultPage.setRecords(uniqueRecords);
                System.out.println("去重后记录数: " + uniqueRecords.size());
            }
            
            return resultPage;
        } catch (Exception e) {
            e.printStackTrace();
            // 发生异常时返回空页面
            Page<EduStudent> emptyPage = new Page<>();
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
    }

    // 根据id查询
    @PostMapping("/findById")
    public EduStudent findById(@RequestBody EduStudent eduStudent) {
        return eduStudentService.getById(eduStudent.getStudentId());
    }

    // 添加
    @PostMapping("/addStu")
    public String addStu(@RequestBody EduStudent eduStudent) {
        boolean save = eduStudentService.save(eduStudent);
        return save?"添加成功":"添加失败";
    }

    // 修改
    @PostMapping("/updateStu")
    public String updateStu(@RequestBody EduStudent eduStudent) {
        try {
            eduStudent.setUpdateTime(new Date());
            
            // 使用UserContext工具类获取当前用户ID
            Integer currentUserId = UserContext.getCurrentUserId();
            if (currentUserId != null) {
                eduStudent.setUpdateBy(currentUserId);
            }
            
            boolean update = eduStudentService.updateById(eduStudent);
            return update ? "修改成功" : "修改失败";
        } catch (Exception e) {
            e.printStackTrace();
            return "修改失败：" + e.getMessage();
        }
    }

    // 删除学生
    @RequestMapping("delStu")
    public String delStu(@RequestParam("id") Integer id) {
        boolean del = eduStudentService.removeById(id);
        return del?"删除成功":"删除失败";
    }

    /**
     * 批量删除学生
     * @param ids 学生ID列表
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public Result batchDeleteStudents(@RequestBody List<Integer> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.ERROR("请选择要删除的学生");
            }

            // 验证学生是否存在
            List<EduStudent> students = eduStudentService.listByIds(ids);
            if (students.size() != ids.size()) {
                return Result.ERROR("部分学生不存在，无法删除");
            }

            // 检查是否有学生正在参与活动或有未处理的积分记录
            // 这里可以根据业务需求添加更多验证逻辑

            // 执行批量删除
            boolean success = eduStudentService.removeByIds(ids);

            if (success) {
                System.out.println("批量删除学生成功，删除数量: " + ids.size());
                return Result.OK("批量删除成功，共删除 " + ids.size() + " 名学生");
            } else {
                return Result.ERROR("批量删除失败");
            }

        } catch (Exception e) {
            System.err.println("批量删除学生失败: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户的学生信息（用于学生端申请）
     * @return 学生信息
     */
    @GetMapping("/getCurrentStudentInfo")
    public Result getCurrentStudentInfo() {
        try {
            // 获取当前登录用户ID
            Integer currentUserId = UserContext.getCurrentUserId();

            // 临时测试：如果用户未登录，使用默认值
            if (currentUserId == null) {
                System.out.println("用户未登录，返回测试学生信息");
                // 返回一个测试学生信息
                EduStudent testStudent = new EduStudent();
                testStudent.setStudentNo("2024001");
                testStudent.setRealName("测试学生");
                testStudent.setClassId(1);
                testStudent.setClassName("测试班级");
                return Result.OK(testStudent);
            }

            // 根据用户ID查询学生信息
            // 假设学生表中有user_id字段关联系统用户表，如果没有则需要通过其他方式关联
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getCreateBy, currentUserId); // 假设createBy字段存储的是用户ID
            wrapper.eq(EduStudent::getDelFlag, 0); // 只查询未删除的学生
            wrapper.last("LIMIT 1"); // 只取第一条记录

            EduStudent student = eduStudentService.getOne(wrapper);
            if (student != null) {
                // 填充班级信息
                if (student.getClassId() != null) {
                    EduClass eduClass = eduClassService.getById(student.getClassId());
                    if (eduClass != null) {
                        student.setClassName(eduClass.getClassName());
                    }
                }
                return Result.OK(student);
            } else {
                return Result.ERROR("未找到学生信息，请联系管理员");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取学生信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据班级ID查询学生列表
     * @param classId 班级ID
     * @return 学生列表
     */
    @GetMapping("/findByClassId")
    public Result findByClassId(@RequestParam("classId") Integer classId) {
        try {
            if (classId == null) {
                return Result.ERROR("班级ID不能为空");
            }
            
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getClassId, classId);
            wrapper.eq(EduStudent::getDelFlag, 0); // 只查询未删除的学生
            wrapper.orderByAsc(EduStudent::getStudentNo); // 按学号排序
            
            List<EduStudent> students = eduStudentService.list(wrapper);
            return Result.OK(students);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级学生失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量导入学生
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/batchImport")
    public Result batchImport(@RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.ERROR("请选择要上传的文件");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                return Result.ERROR("文件名不能为空");
            }
            
            // 检查文件格式
            String lowercaseFilename = originalFilename.toLowerCase();
            if (!lowercaseFilename.endsWith(".xlsx") && !lowercaseFilename.endsWith(".xls") 
                && !lowercaseFilename.endsWith(".et") && !lowercaseFilename.endsWith(".ett")) {
                return Result.ERROR("不支持的文件格式，请上传Excel格式文件(.xlsx/.xls)");
            }
            
            // 检查文件大小
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB
                return Result.ERROR("文件大小不能超过10MB");
            }
            
            // 处理导入
            Map<String, Object> result = eduStudentService.batchImportStudents(file);
            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("导入失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取所有班级信息，用于学生导入时选择
     */
    @GetMapping("/getClassOptions")
    public Result getClassOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        List<EduClass> classList = eduClassService.list();
        for (EduClass eduClass : classList) {
            Map<String, Object> option = new HashMap<>();
            option.put("classId", eduClass.getClassId());
            option.put("className", eduClass.getClassName());
            options.add(option);
        }
        return Result.OK(options);
    }
    
    /**
     * 下载学生导入模板
     */
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        XSSFWorkbook workbook = null;
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("学生导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            
            // 创建Excel工作簿
            workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生导入");
            
            // 设置列宽
            sheet.setColumnWidth(0, 15 * 256); // 学号
            sheet.setColumnWidth(1, 10 * 256); // 姓名
            sheet.setColumnWidth(2, 6 * 256);  // 性别
            sheet.setColumnWidth(3, 15 * 256); // 班级
            sheet.setColumnWidth(4, 15 * 256); // 联系电话
            sheet.setColumnWidth(5, 25 * 256); // 邮箱
            sheet.setColumnWidth(6, 30 * 256); // 备注
            
            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            
            // 创建必填字段样式
            CellStyle requiredStyle = workbook.createCellStyle();
            requiredStyle.setAlignment(HorizontalAlignment.CENTER);
            requiredStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            requiredStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
            requiredStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            requiredStyle.setBorderTop(BorderStyle.THIN);
            requiredStyle.setBorderRight(BorderStyle.THIN);
            requiredStyle.setBorderBottom(BorderStyle.THIN);
            requiredStyle.setBorderLeft(BorderStyle.THIN);
            
            Font requiredFont = workbook.createFont();
            requiredFont.setBold(true);
            requiredFont.setColor(IndexedColors.RED.getIndex());
            requiredStyle.setFont(requiredFont);
            
            // 创建普通内容样式
            CellStyle contentStyle = workbook.createCellStyle();
            contentStyle.setAlignment(HorizontalAlignment.CENTER);
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentStyle.setBorderTop(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);
            contentStyle.setBorderBottom(BorderStyle.THIN);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            
            // 创建说明样式
            CellStyle noteStyle = workbook.createCellStyle();
            noteStyle.setAlignment(HorizontalAlignment.LEFT);
            noteStyle.setWrapText(true);
            Font noteFont = workbook.createFont();
            noteFont.setColor(IndexedColors.RED.getIndex());
            noteStyle.setFont(noteFont);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"学号", "姓名", "性别", "班级", "联系电话", "邮箱", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建是否必填行
            Row requiredRow = sheet.createRow(1);
            String[] requiredFields = {"必填", "必填", "必填", "必填", "选填", "选填", "选填"};
            for (int i = 0; i < requiredFields.length; i++) {
                Cell cell = requiredRow.createCell(i);
                cell.setCellValue(requiredFields[i]);
                if (requiredFields[i].equals("必填")) {
                    cell.setCellStyle(requiredStyle);
                } else {
                    cell.setCellStyle(contentStyle);
                }
            }
            
            // 创建字段说明行
            Row descriptionRow = sheet.createRow(2);
            String[] descriptions = {
                "必须为7-20位字符，且不能重复", 
                "2-20个字符", 
                "男/女", 
                "必须是系统中已存在的班级", 
                "11位手机号码格式", 
                "有效的邮箱格式", 
                "最多500个字符"
            };
            for (int i = 0; i < descriptions.length; i++) {
                Cell cell = descriptionRow.createCell(i);
                cell.setCellValue(descriptions[i]);
                cell.setCellStyle(contentStyle);
            }
            
            // 创建示例数据行
            Row exampleRow = sheet.createRow(3);
            
            // 获取系统中的一个班级名称作为示例
            String exampleClassName = "2409A"; // 默认示例班级名
            try {
                List<EduClass> classes = eduClassService.list(new LambdaQueryWrapper<EduClass>().last("limit 1"));
                if (classes != null && !classes.isEmpty() && classes.get(0).getClassName() != null) {
                    exampleClassName = classes.get(0).getClassName();
                }
            } catch (Exception e) {
                // 忽略异常，使用默认示例
            }
            
            String[] exampleData = {
                "20210501001", 
                "张三", 
                "男", 
                exampleClassName, 
                "13800138000", 
                "<EMAIL>", 
                "示例数据"
            };
            for (int i = 0; i < exampleData.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(exampleData[i]);
                cell.setCellStyle(contentStyle);
            }
            
            // 添加批量导入说明
            Row titleRow = sheet.createRow(5);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("批量导入学生信息说明");
            CellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.LEFT);
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 12);
            titleStyle.setFont(titleFont);
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, 6));
            
            // 添加导入步骤说明
            String[] steps = {
                "1. 下载模板：点击\"下载模板\"按钮获取标准Excel导入模板",
                "2. 填写数据：按照模板格式填写学生信息",
                "3. 上传文件：将填写好的Excel文件上传",
                "4. 预览数据：系统会自动检查数据格式，标记错误数据",
                "5. 确认导入：确认无误后点击\"开始导入\"按钮"
            };
            
            for (int i = 0; i < steps.length; i++) {
                Row stepRow = sheet.createRow(6 + i);
                Cell stepCell = stepRow.createCell(0);
                stepCell.setCellValue(steps[i]);
                stepCell.setCellStyle(noteStyle);
                sheet.addMergedRegion(new CellRangeAddress(6 + i, 6 + i, 0, 6));
            }
            
            // 添加注意事项
            Row noteRow = sheet.createRow(12);
            Cell noteCell = noteRow.createCell(0);
            noteCell.setCellValue("注意事项：");
            Font noteTitleFont = workbook.createFont();
            noteTitleFont.setBold(true);
            noteTitleFont.setColor(IndexedColors.RED.getIndex());
            CellStyle noteTitleStyle = workbook.createCellStyle();
            noteTitleStyle.setFont(noteTitleFont);
            noteCell.setCellStyle(noteTitleStyle);
            
            String[] notes = {
                "1. 学号必须唯一，系统会自动检查重复学号",
                "2. 性别只能填写\"男\"或\"女\"",
                "3. 班级必须是系统中已存在的班级",
                "4. 请确保Excel文件格式正确，避免使用特殊字符"
            };
            
            for (int i = 0; i < notes.length; i++) {
                Row noteItemRow = sheet.createRow(13 + i);
                Cell noteItemCell = noteItemRow.createCell(0);
                noteItemCell.setCellValue(notes[i]);
                noteItemCell.setCellStyle(noteStyle);
                sheet.addMergedRegion(new CellRangeAddress(13 + i, 13 + i, 0, 6));
            }
            
            // 添加系统中存在的班级列表
            Row classListTitleRow = sheet.createRow(18);
            Cell classListTitleCell = classListTitleRow.createCell(0);
            classListTitleCell.setCellValue("系统中已存在的班级列表：");
            CellStyle classListTitleStyle = workbook.createCellStyle();
            classListTitleStyle.setFont(noteTitleFont);
            classListTitleCell.setCellStyle(classListTitleStyle);
            sheet.addMergedRegion(new CellRangeAddress(18, 18, 0, 6));
            
            // 获取所有班级
            List<EduClass> allClasses = eduClassService.list();
            if (allClasses != null && !allClasses.isEmpty()) {
                int classRowIndex = 19;
                int classesPerRow = 4;
                int currentClassInRow = 0;
                Row classRow = sheet.createRow(classRowIndex);
                
                for (EduClass eduClass : allClasses) {
                    if (eduClass.getClassName() != null) {
                        if (currentClassInRow >= classesPerRow) {
                            classRowIndex++;
                            classRow = sheet.createRow(classRowIndex);
                            currentClassInRow = 0;
                        }
                        
                        Cell classCell = classRow.createCell(currentClassInRow);
                        classCell.setCellValue(eduClass.getClassName());
                        CellStyle classStyle = workbook.createCellStyle();
                        classStyle.setAlignment(HorizontalAlignment.LEFT);
                        classCell.setCellStyle(classStyle);
                        
                        currentClassInRow++;
                    }
                }
            } else {
                Row noClassRow = sheet.createRow(19);
                Cell noClassCell = noClassRow.createCell(0);
                noClassCell.setCellValue("暂无班级数据");
                noClassCell.setCellStyle(contentStyle);
            }
            
            // 写入响应流
            workbook.write(response.getOutputStream());
            response.flushBuffer(); // 确保数据被刷新到客户端
        } catch (Exception e) {
            e.printStackTrace();
            try {
                // 重置响应，防止部分内容已经写入
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"code\":500,\"message\":\"模板下载失败：" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 导出学生数据
     */
    @PostMapping("/export")
    public void export(@RequestBody EduStudent student, HttpServletResponse response) {
        XSSFWorkbook workbook = null;
        try {
            // 设置响应头，防止中文乱码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 添加日期到文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String dateStr = sdf.format(new Date());
            String fileName = URLEncoder.encode("学生数据_" + dateStr, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            // 防止浏览器缓存
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            
            // 查询数据
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(StringUtils.isNotBlank(student.getRealName()), EduStudent::getRealName, student.getRealName());
            wrapper.eq(StringUtils.isNotBlank(student.getStudentNo()), EduStudent::getStudentNo, student.getStudentNo());
            wrapper.eq(student.getClassId() != null, EduStudent::getClassId, student.getClassId());
            wrapper.eq(student.getGender() != null, EduStudent::getGender, student.getGender());
            
            // 处理专业筛选
            if (StringUtils.isNotBlank(student.getStageName())) {
                String stageName = student.getStageName().trim();
                // 先找到对应专业的ID
                List<EduStage> stages = eduStageService.list(new LambdaQueryWrapper<EduStage>()
                    .like(EduStage::getStageName, stageName));
                
                if (!stages.isEmpty()) {
                    // 获取这些专业下的所有班级ID
                    List<Integer> stageIds = stages.stream().map(EduStage::getStageId).collect(Collectors.toList());
                    List<EduClass> classes = eduClassService.list(new LambdaQueryWrapper<EduClass>()
                        .in(EduClass::getStageId, stageIds));
                    
                    if (!classes.isEmpty()) {
                        // 获取这些班级的所有学生
                        List<Integer> classIds = classes.stream().map(EduClass::getClassId).collect(Collectors.toList());
                        wrapper.in(EduStudent::getClassId, classIds);
                    } else {
                        // 没有找到匹配的班级，返回空结果
                        wrapper.eq(EduStudent::getStudentId, -1); // 不存在的ID，确保结果为空
                    }
                } else {
                    // 没有找到匹配的专业，返回空结果
                    wrapper.eq(EduStudent::getStudentId, -1); // 不存在的ID，确保结果为空
                }
            }
            
            List<EduStudent> list = eduStudentService.list(wrapper);
            System.out.println("导出学生数据，查询结果：" + list.size() + "条记录");
            
            // 处理班级和专业名称
            for (EduStudent record : list) {
                if (record.getClassId() != null) {
                EduClass eduClass = eduClassService.getById(record.getClassId());
                if (eduClass != null) {
                    record.setClassName(eduClass.getClassName());
                        if (eduClass.getStageId() != null) {
                    EduStage eduStage = eduStageService.getById(eduClass.getStageId());
                    if (eduStage != null) {
                        record.setStageName(eduStage.getStageName());
                    }
                }
            }
                }
            }
            
            // 创建Excel工作簿
            workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生数据");
            
            // 设置列宽
            sheet.setColumnWidth(0, 15 * 256); // 学号
            sheet.setColumnWidth(1, 10 * 256); // 姓名
            sheet.setColumnWidth(2, 6 * 256);  // 性别
            sheet.setColumnWidth(3, 20 * 256); // 班级
            sheet.setColumnWidth(4, 20 * 256); // 专业
            sheet.setColumnWidth(5, 8 * 256);  // 积分
            sheet.setColumnWidth(6, 15 * 256); // 联系电话
            sheet.setColumnWidth(7, 25 * 256); // 邮箱
            sheet.setColumnWidth(8, 10 * 256); // 状态
            sheet.setColumnWidth(9, 30 * 256); // 备注
            
            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            
            // 创建内容样式
            CellStyle contentStyle = workbook.createCellStyle();
            contentStyle.setAlignment(HorizontalAlignment.CENTER);
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentStyle.setBorderTop(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);
            contentStyle.setBorderBottom(BorderStyle.THIN);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short)(400));
            
            String[] headers = {"学号", "姓名", "性别", "班级", "专业", "积分", "联系电话", "邮箱", "状态", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 填充数据
            if (list.isEmpty()) {
                Row emptyRow = sheet.createRow(1);
                Cell emptyCell = emptyRow.createCell(0);
                emptyCell.setCellValue("没有符合条件的数据");
                emptyCell.setCellStyle(contentStyle);
                sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headers.length - 1));
            } else {
            int rowNum = 1;
            for (EduStudent record : list) {
                Row row = sheet.createRow(rowNum++);
                
                    createCell(row, 0, record.getStudentNo(), contentStyle);
                    createCell(row, 1, record.getRealName(), contentStyle);
                    createCell(row, 2, record.getGender() == null ? "未知" : (record.getGender() == 1 ? "男" : "女"), contentStyle);
                    createCell(row, 3, record.getClassName(), contentStyle);
                    createCell(row, 4, record.getStageName(), contentStyle);
                    createCell(row, 5, record.getPoints() == null ? "0" : record.getPoints().toString(), contentStyle);
                    createCell(row, 6, record.getPhone(), contentStyle);
                    createCell(row, 7, record.getEmail(), contentStyle);
                
                String status = "未知";
                if (record.getStatus() != null) {
                    switch (record.getStatus()) {
                        case 0:
                            status = "正常";
                            break;
                        case 1:
                            status = "休学";
                            break;
                        case 2:
                            status = "退学";
                            break;
                    }
                }
                    createCell(row, 8, status, contentStyle);
                    createCell(row, 9, record.getRemark(), contentStyle);
                }
            }
            
            // 输出到响应流
            workbook.write(response.getOutputStream());
            System.out.println("导出学生数据完成");
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("导出学生数据失败: " + e.getMessage());
            try {
                // 发生异常时返回错误信息
                response.reset(); // 重置响应
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } finally {
            // 关闭工作簿
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 创建单元格并设置值和样式
     */
    private void createCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value == null ? "" : value);
        cell.setCellStyle(style);
    }


    @RequestMapping("addPoints")
    public Result addPoints(Integer studentId, Integer points){
        EduStudent byId = eduStudentService.getById(studentId);
        if(byId.getDelFlag().equals("1")){
            return Result.ERROR("该学生已删除，加分失败");
        }
        if (byId.getStatus().equals("1")){
            return Result.ERROR("该学生已休学，加分失败");
        }
        if (byId.getStatus().equals("2")){
            return Result.ERROR("该学生已退学，加分失败");
        }
        if (byId.getStatus().equals("3")){
            return Result.ERROR("该学生已毕业，加分失败");
        }
        byId.setPoints(byId.getPoints()+points);
        byId.setUpdateBy(UserContext.getCurrentUser().getUserId());
        byId.setUpdateTime(new Date());
        boolean b = eduStudentService.updateById(byId);
        return b?Result.OK("加分成功"):Result.ERROR("加分失败");
    }
    @RequestMapping("minusPoints")
    public Result minusPoints(Integer studentId, Integer points){
        EduStudent byId = eduStudentService.getById(studentId);
        if(byId.getDelFlag().equals("1")){
            return Result.ERROR("该学生已删除，减分失败");
        }
        if (byId.getStatus().equals("1")){
            return Result.ERROR("该学生已休学，减分失败");
        }
        if (byId.getStatus().equals("2")){
            return Result.ERROR("该学生已退学，减分失败");
        }
        if (byId.getStatus().equals("3")){
            return Result.ERROR("该学生已毕业，减分失败");
        }
        byId.setPoints(byId.getPoints()-points);
        byId.setUpdateBy(UserContext.getCurrentUser().getUserId());
        byId.setUpdateTime(new Date());
        boolean b = eduStudentService.updateById(byId);
        return b?Result.OK("减分成功"):Result.ERROR("减分失败");
    }

    @GetMapping("search")
    public Result search(@RequestParam(required = false, defaultValue = "") String keyword) {
        try {
            List<EduStudent> students = eduStudentService.searchStudents(keyword);
            return Result.OK(students);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询学生信息失败: " + e.getMessage());
        }
    }
/* ******************************************************************************************************************* */
/*
 * 下载学生信息导入模板
 * @param response HTTP响应
 */
@GetMapping("/template/download")
public void downloadStudentTemplate(HttpServletResponse response) {
    try {
        eduStudentService.downloadStudentTemplate(response);
    } catch (Exception e) {
        e.printStackTrace();
        try {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"message\":\"模板下载失败：" + e.getMessage() + "\"}");
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
}

    /*
     * 批量导入学生信息 - 高性能优化版本
     * @param importData 导入数据
     * @return 导入结果
     */
    @PostMapping("/batch/import")
    @Transactional(rollbackFor = Exception.class)
    public Result batchImportStudents(@RequestBody BatchImportDto importData) {
        long startTime = System.currentTimeMillis();
        try {
            if (importData == null || importData.getClasses() == null || importData.getClasses().isEmpty()) {
                return Result.ERROR("导入数据不能为空");
            }

            // 统计导入数据量
            int totalStudents = importData.getClasses().stream()
                    .mapToInt(classDto -> classDto.getStudents().size())
                    .sum();

            System.out.println("开始高性能批量导入，班级数量: " + importData.getClasses().size() +
                             "，学生总数: " + totalStudents);

            BatchImportResultDto result = eduStudentService.batchImportStudentsFromDto(importData);

            // 记录完成时间
            long endTime = System.currentTimeMillis();
            double timeInSeconds = (endTime - startTime) / 1000.0;
            System.out.println("高性能批量导入完成，耗时: " + timeInSeconds + " 秒，" +
                             "成功: " + result.getSuccessCount() + "，失败: " + result.getFailCount());

            return Result.OK(result);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            double timeInSeconds = (endTime - startTime) / 1000.0;
            System.err.println("批量导入失败，耗时: " + timeInSeconds + " 秒，错误: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("批量导入失败: " + e.getMessage());
        }
    }

    /*
     * 重置表ID自增的API端点
     * @param resetStudentTable 是否重置学生表（默认true）
     * @param resetClassTable 是否重置班级表（默认false）
     * @param resetStageTable 是否重置阶段表（默认false）
     * @param resetOldStuTable 是否重置历史学生表（默认false）
     * @return 重置结果
     */
    @PostMapping("/reset-auto-increment")
    public Result resetAutoIncrement(
            @RequestParam(defaultValue = "true") boolean resetStudentTable,
            @RequestParam(defaultValue = "false") boolean resetClassTable,
            @RequestParam(defaultValue = "false") boolean resetStageTable,
            @RequestParam(defaultValue = "false") boolean resetOldStuTable) {

        long startTime = System.currentTimeMillis();
        try {
            System.out.println("手动重置表ID自增请求 - 学生表:" + resetStudentTable +
                             ", 班级表:" + resetClassTable +
                             ", 阶段表:" + resetStageTable +
                             ", 历史表:" + resetOldStuTable);

            eduStudentService.resetTableAutoIncrement(resetStudentTable, resetClassTable, resetStageTable, resetOldStuTable);

            long endTime = System.currentTimeMillis();
            System.out.println("手动重置表ID自增完成，耗时: " + (endTime - startTime) + "ms");

            return Result.OK("表ID自增重置成功");

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            System.err.println("手动重置表ID自增失败，耗时: " + (endTime - startTime) + "ms，错误: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("重置表ID自增失败：" + e.getMessage());
        }
    }

    /*
     * 快速重置学生表ID自增的API端点
     * 只重置edu_student表，用于快速操作
     * @return 重置结果
     */
    @PostMapping("/reset-student-auto-increment")
    public Result resetStudentAutoIncrement() {
        long startTime = System.currentTimeMillis();
        try {
            System.out.println("快速重置学生表ID自增请求");

            // 直接执行SQL重置学生表
            jdbcTemplate.execute("ALTER TABLE edu_student AUTO_INCREMENT = 1");

            long endTime = System.currentTimeMillis();
            System.out.println("快速重置学生表ID自增完成，耗时: " + (endTime - startTime) + "ms");

            return Result.OK("学生表ID自增重置成功");

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            System.err.println("快速重置学生表ID自增失败，耗时: " + (endTime - startTime) + "ms，错误: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("重置学生表ID自增失败：" + e.getMessage());
        }
    }

    /*
     * 验证导入数据
     * @param importData 导入数据
     * @return 验证结果
     */
    @PostMapping("/batch/validate")
    public Result validateImportData(@RequestBody BatchImportDto importData) {
        try {
            if (importData == null || importData.getClasses() == null || importData.getClasses().isEmpty()) {
                return Result.ERROR("验证数据不能为空");
            }

            // 调用验证方法
            List<BatchImportDto.ClassImportDto> validatedClasses = eduStudentService.validateImportData(importData);
            return Result.OK(validatedClasses);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("数据验证失败：" + e.getMessage());
        }
    }

    //---------------------------------------------------横线一下是cmy写的代码--------------------------------------------------------

    /**
     * 查询积分前十名的学生数据
     */
    @PostMapping("/queryTopTenStudents")
    public Result queryTopTenStudents() {
        return eduStudentService.queryTopTenStudents();
    }
    /**
     * 查询积分后十名学生的数据
     */
    @PostMapping("/queryBottomTenStudents")
    public Result queryLastTenStudents() {
        return eduStudentService.queryBottomTenStudents();
    }
    /**
     * 查询积分前十名的学生数据
     */
    @PostMapping("/queryTopTenStudentsByClass")
    public Result queryTopTenStudentsByClass(Integer classId) {
        return eduStudentService.queryTopTenStudentsByClass(classId);
    }
    /**
     * 查询积分后十名学生的数据
     */
    @PostMapping("/queryBottomTenStudentsByClass")
    public Result queryLastTenStudentsClass(Integer classId) {
        return eduStudentService.queryBottomTenStudentsByClass(classId);
    }
}

package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.PointsRecord;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.PointsRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 积分变动记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/points-record")
public class PointsRecordController {

    @Autowired
    private PointsRecordService pointsRecordService;
    @Autowired
    private EduStudentService eduStudentService;

    /**
     * 查询积分变动记录表
     *
     * @return
     */
    @PostMapping("/findAll")
    public List<PointsRecord> findAll() {
        // 根据lambda表达式,编写查询条件
        LambdaQueryWrapper<PointsRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(PointsRecord::getCreateTime);
        wrapper.eq(PointsRecord::getDelFlag, 0);
        wrapper.eq(PointsRecord::getOperationType, 3);
        List<PointsRecord> list = pointsRecordService.list(wrapper);
        for (PointsRecord pointsRecord : list) {
            QueryWrapper<EduStudent> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("student_no", pointsRecord.getStudentNo());
            EduStudent student = eduStudentService.getOne(queryWrapper);
            // 判断student是否为空
            if (student == null) {
                continue;
            }
            pointsRecord.setStudentName(student.getRealName());
        }
        // 过滤list中student为空的元素
        return list.stream().filter(pointsRecord -> pointsRecord.getStudentName() != null).toList();
    }
}

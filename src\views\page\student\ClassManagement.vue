<template>
  <div class="class-management-container">
    <!-- 筛选和操作区域 -->
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon class="header-icon"><School /></el-icon>
            班级信息管理
          </h3>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            添加班级
          </el-button>
        </div>
      </template>

      <el-form :inline="true" class="filter-form">
        <el-form-item label="班级名称">
          <el-input
            v-model="searchText"
            placeholder="请输入班级名称"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="refreshTable" :icon="Refresh">刷新</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 班级列表 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon class="header-icon"><School /></el-icon>
            班级列表
          </h3>
          <div class="header-actions">
            <span class="total-info">共 {{ total }} 条记录</span>
          </div>
        </div>
      </template>

      <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="className" label="班级名称" min-width="120" />
        <el-table-column prop="remark" label="所属专业" min-width="150" />
        <el-table-column prop="classroom" label="教室号" width="100" />
        <el-table-column label="讲师" width="100">
          <template #default="scope">
            <el-tooltip :content="`ID: ${scope.row.teacherId || '未设置'}`" placement="top" effect="light">
              <span>{{ getTeacherName(scope.row.teacherId) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="导员" width="100">
          <template #default="scope">
            <el-tooltip :content="`ID: ${scope.row.counselorId || '未设置'}`" placement="top" effect="light">
              <span>{{ getCounselorName(scope.row.counselorId) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="开班日期" width="110" sortable>
          <template #default="scope">
            <el-tooltip :content="scope.row.startDate || scope.row.createTime || '未设置'" placement="top" effect="light">
              <span>{{ formatDate(scope.row.startDate || scope.row.createTime) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="班级人数" width="90" sortable>
          <template #default="scope">
            <el-tag :type="getCountTagType(scope.row.students ? scope.row.students.length : 0)">
              {{ scope.row.students ? scope.row.students.length : 0 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'info'">
              {{ scope.row.status === 0 ? '正常' : '结束' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewStudents(scope.row)">查看学生</el-button>
            <el-button link type="primary" size="small" @click="editClass(scope.row)">编辑</el-button>
            <el-button link type="danger" size="small" @click="handleDeleteClass(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑班级对话框 -->
    <el-dialog
      v-model="classDialogVisible"
      :title="isEdit ? '编辑班级' : '添加班级'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="classForm" :rules="classRules" ref="classFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="班级名称" prop="className">
              <el-input v-model="classForm.className" placeholder="请输入班级名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="教室号" prop="classroom">
              <el-input v-model="classForm.classroom" placeholder="请输入教室号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="所属阶段" prop="stageId">
          <el-select v-model="classForm.stageId" placeholder="请选择专业" style="width: 100%">
            <el-option
              v-for="item in stageOptions"
              :key="item.stageId"
              :label="`${item.stageName}${item.courseName ? ' - ' + item.courseName : ''}`"
              :value="item.stageId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="讲师" prop="teacherId">
              <el-select v-model="classForm.teacherId" placeholder="请选择讲师" style="width: 100%">
                <el-option
                  v-for="teacher in teacherOptions"
                  :key="teacher.userId"
                  :label="teacher.realName"
                  :value="teacher.userId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="导员" prop="counselorId">
              <el-select v-model="classForm.counselorId" placeholder="请选择导员" style="width: 100%">
                <el-option
                  v-for="counselor in counselorOptions"
                  :key="counselor.userId"
                  :label="counselor.realName"
                  :value="counselor.userId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="阶段主任" prop="directorId">
              <el-select v-model="classForm.directorId" placeholder="请选择阶段主任" style="width: 100%">
                <el-option
                  v-for="director in directorOptions"
                  :key="director.userId"
                  :label="director.realName"
                  :value="director.userId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开班日期" prop="createTime">
              <el-date-picker
                v-model="classForm.createTime"
                type="date"
                placeholder="选择开班日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="状态" prop="status">
          <el-select v-model="classForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option :value="0" label="正常"></el-option>
            <el-option :value="1" label="结束"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="classForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="classDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitClassForm" :loading="submitting">
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 班级学生列表对话框 -->
    <el-dialog v-model="studentsDialogVisible" :title="`${currentClass.className || ''}学生列表`" width="80%">
      <el-table :data="classStudents" style="width: 100%" border stripe v-loading="studentsLoading">
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="studentNo" label="学号" width="120" />
        <el-table-column prop="realName" label="姓名" width="100" />
        <el-table-column label="性别" width="60">
          <template #default="scope">
            <span v-if="scope.row.gender === null">未知</span>
            <span v-else-if="scope.row.gender === 1">男</span>
            <span v-else>女</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="积分" width="80">
          <template #default="scope">
            <span :class="getPointsClass(scope.row.points)">{{ scope.row.points }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStudentStatusType(scope.row.status)">
              {{ getStudentStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="class-statistics">
        <el-descriptions title="班级统计" :column="4" border>
          <el-descriptions-item label="班级人数">{{ classStatistics.totalStudents || 0 }}</el-descriptions-item>
          <el-descriptions-item label="平均积分">{{ classStatistics.averagePoints || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最高积分">{{ classStatistics.maxPoints || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最低积分">{{ classStatistics.minPoints || 0 }}</el-descriptions-item>
          <el-descriptions-item label="男生人数">{{ classStatistics.maleCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="女生人数">{{ classStatistics.femaleCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="专业名称">{{ classStatistics.stageName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="班级状态">{{ currentClass.status === 0 ? '正常' : '结束' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="studentsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleExportClassStudents">导出学生名单</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search, School, Plus } from '@element-plus/icons-vue'
import { getClassList, addClass, updateClass, deleteClass, getClassStudents, getClassStatistics, exportClassStudents, getStageList } from '@/api/system/class'
import { getUsersByType } from '@/api/system/user'
import { useRouter } from 'vue-router'
import { format } from 'date-fns'

const router = useRouter()

// 专业选项
const stageOptions = ref([])
const teacherOptions = ref([])
const counselorOptions = ref([])
const directorOptions = ref([])

// 表单数据
const classFormRef = ref(null)
const isEdit = ref(false)
const submitting = ref(false)
const classForm = reactive({
  classId: null,
  className: '',
  stageId: null,
  classroom: '',
  teacherId: null,
  counselorId: null,
  directorId: null,
  createTime: '',
  status: 0,
  remark: ''
})

// 表单验证规则
const classRules = {
  className: [
    { required: true, message: '请输入班级名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  stageId: [
    { required: true, message: '请选择所属专业', trigger: 'change' }
  ],
  classroom: [
    { required: true, message: '请输入教室号', trigger: 'blur' }
  ],
  teacherId: [
    { required: true, message: '请选择讲师', trigger: 'change' }
  ],
  counselorId: [
    { required: true, message: '请选择导员', trigger: 'change' }
  ],
  createTime: [
    { required: true, message: '请选择开班日期', trigger: 'change' }
  ]
}

// 班级列表数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索
const searchText = ref('')

// 班级表单对话框
const classDialogVisible = ref(false)

// 班级学生对话框
const studentsDialogVisible = ref(false)
const studentsLoading = ref(false)
const classStudents = ref([])
const currentClass = reactive({})
const classStatistics = reactive({})

// 获取专业列表
const fetchStageOptions = async () => {
  try {
    const res = await getStageList()
    // 修复：处理不同的响应格式
    if (res.data && res.data.code === 200) {
      // 如果是标准Result格式
      stageOptions.value = res.data.data || []
    } else {
      // 兼容旧格式
    stageOptions.value = res.data.result || []
    }
  } catch (error) {
    console.error('获取专业列表失败:', error)
    ElMessage.error('获取专业列表失败')
  }
}

// 获取用户列表（讲师、导员、主任）
const fetchUserOptions = async () => {
  try {
    // 获取讲师（用户类型1）
    const teachersRes = await getUsersByType(1)
    // 修复：处理不同的响应格式
    if (teachersRes.data && teachersRes.data.code === 200) {
      teacherOptions.value = teachersRes.data.data || []
    } else {
    teacherOptions.value = teachersRes.data.result || []
    }
    
    // 获取导员（用户类型2）
    const counselorsRes = await getUsersByType(2)
    if (counselorsRes.data && counselorsRes.data.code === 200) {
      counselorOptions.value = counselorsRes.data.data || []
    } else {
    counselorOptions.value = counselorsRes.data.result || []
    }
    
    // 获取阶段主任（用户类型3）
    const directorsRes = await getUsersByType(3)
    if (directorsRes.data && directorsRes.data.code === 200) {
      directorOptions.value = directorsRes.data.data || []
    } else {
    directorOptions.value = directorsRes.data.result || []
    }
    
    console.log('讲师列表:', teacherOptions.value)
    console.log('导员列表:', counselorOptions.value)
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 获取讲师名称
const getTeacherName = (teacherId) => {
  if (!teacherId) return '-'
  const teacher = teacherOptions.value.find(item => item.userId === teacherId)
  if (teacher) {
    return teacher.realName || teacher.username || '-'
  }
  return '-'
}

// 获取导员名称
const getCounselorName = (counselorId) => {
  if (!counselorId) return '-'
  const counselor = counselorOptions.value.find(item => item.userId === counselorId)
  if (counselor) {
    return counselor.realName || counselor.username || '-'
  }
  return '-'
}

// 获取班级列表
const fetchClassList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      className: searchText.value
    }
    
    const res = await getClassList(params)
    console.log('班级列表数据:', res.data)
    
    if (res.data && res.data.records) {
    tableData.value = res.data.records || []
    total.value = res.data.total || 0
      
      // 打印一些调试信息，查看班级数据
      if (tableData.value.length > 0) {
        const firstClass = tableData.value[0]
        console.log('第一个班级数据:', firstClass)
        console.log('讲师ID:', firstClass.teacherId)
        console.log('导员ID:', firstClass.counselorId)
        console.log('开始日期:', firstClass.createTime)
      }
    } else if (res.data && res.data.code === 200 && res.data.data) {
      // 处理标准Result格式
      const data = res.data.data
      tableData.value = data.records || []
      total.value = data.total || 0
    } else {
      tableData.value = []
      total.value = 0
      console.warn('获取班级列表返回的数据格式不符合预期:', res.data)
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

// 添加/更新班级
const submitClassForm = async () => {
  if (!classFormRef.value) return

  await classFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请正确填写表单信息')
      return
    }

    submitting.value = true
    try {
      const formData = { ...classForm }

      console.log('提交前的表单数据:', formData)

      // 数据清理和转换
      // 1. 将createTime转换为startDate（后端需要的字段名）
      if (formData.createTime) {
        formData.startDate = formData.createTime
        delete formData.createTime
      }

      // 2. 移除不需要的字段
      delete formData.pageNum
      delete formData.pageSize
      delete formData.students

      // 3. 确保所有必要字段都存在
      if (!formData.className) {
        throw new Error('班级名称不能为空')
      }

      if (!formData.stageId) {
        throw new Error('所属专业不能为空')
      }

      if (!formData.teacherId) {
        throw new Error('讲师不能为空')
      }

      console.log('处理后的表单数据:', formData)

      if (isEdit.value) {
        // 更新班级
        const updateRes = await updateClass(formData)
        console.log('更新班级响应:', updateRes)

        if (updateRes.data && updateRes.data.code === 200) {
          ElMessage.success(updateRes.data.message || '更新班级成功')
          // 关闭对话框
          classDialogVisible.value = false
          // 重置表单
          resetForm()
          // 刷新班级列表
          fetchClassList()
        } else {
          throw new Error(updateRes.data?.message || '更新失败，请检查数据格式')
        }
      } else {
        // 添加班级
        const addRes = await addClass(formData)
        console.log('添加班级响应:', addRes)

        if (addRes.data && addRes.data.code === 200) {
          ElMessage.success(addRes.data.message || '添加班级成功')
          // 关闭对话框
          classDialogVisible.value = false
          // 重置表单
          resetForm()
          // 刷新班级列表
          fetchClassList()
        } else {
          throw new Error(addRes.data?.message || '添加失败，请检查数据格式')
        }
      }
    } catch (error) {
      console.error('表单提交失败:', error)
      ElMessage.error(error.message || '操作失败')
    } finally {
      submitting.value = false
    }
  })
}

// 显示添加班级对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  classDialogVisible.value = true
}

// 编辑班级
const editClass = (row) => {
  isEdit.value = true

  console.log('编辑班级数据:', row)

  // 复制班级数据到表单
  Object.keys(classForm).forEach(key => {
    if (key === 'createTime') {
      // 日期特殊处理：后端返回的是startDate，需要转换为createTime
      const dateValue = row.startDate || row.createTime
      if (dateValue) {
        try {
          // 统一转换为YYYY-MM-DD格式
          const date = new Date(dateValue)
          if (!isNaN(date.getTime())) {
            classForm[key] = format(date, 'yyyy-MM-dd')
          } else {
            classForm[key] = ''
          }
        } catch (e) {
          console.error(`处理${key}出错:`, e)
          classForm[key] = ''
        }
      } else {
        classForm[key] = ''
      }
    } else {
      classForm[key] = row[key]
    }
  })

  console.log('表单数据:', classForm)

  // 显示对话框
  classDialogVisible.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEdit.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (classFormRef.value) {
    classFormRef.value.resetFields()
  }
  
  isEdit.value = false
  
  // 清空表单数据
  Object.keys(classForm).forEach(key => {
    if (key === 'status') {
      classForm[key] = 0
    } else {
      classForm[key] = null
    }
  })
  
  classForm.className = ''
  classForm.classroom = ''
  classForm.createTime = ''
  classForm.remark = ''
}

// 删除班级
const handleDeleteClass = (row) => {
  ElMessageBox.confirm(`确定要删除班级"${row.className}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteClass(row.classId)
      ElMessage.success('删除班级成功')
      fetchClassList()
    } catch (error) {
      console.error('删除班级失败:', error)
      ElMessage.error('删除班级失败')
    }
  }).catch(() => {
    // 用户取消删除，不做处理
  })
}

// 查看班级学生
const viewStudents = async (row) => {
  currentClass.classId = row.classId
  currentClass.className = row.className
  currentClass.status = row.status
  
  studentsDialogVisible.value = true
  studentsLoading.value = true
  
  try {
    // 获取班级学生
    const studentsRes = await getClassStudents(row.classId)
    console.log('获取到的学生数据:', studentsRes)
    
    // 修复：正确处理后端返回的数据格式
    // 后端返回的是Result对象，学生数据在data字段中
    if (studentsRes.data && studentsRes.data.code === 200) {
      // 成功获取数据
      classStudents.value = studentsRes.data.data || []
    } else {
      console.warn('获取到的学生数据格式不符合预期:', studentsRes)
      classStudents.value = []
      ElMessage.warning('获取班级学生数据格式异常')
    }
    
    // 获取班级统计数据
    const statsRes = await getClassStatistics(row.classId)
    if (statsRes.data && statsRes.data.code === 200) {
      Object.assign(classStatistics, statsRes.data.data || {})
    } else {
      console.warn('获取到的班级统计数据格式异常:', statsRes)
      // 保持默认的空对象
    }
  } catch (error) {
    console.error('获取班级学生数据失败:', error)
    ElMessage.error('获取班级学生数据失败')
  } finally {
    studentsLoading.value = false
  }
}

// 导出班级学生名单
const handleExportClassStudents = async () => {
  try {
    const response = await exportClassStudents(currentClass.classId)
    
    // 创建Blob对象
    const blob = new Blob([response.data], { type: response.headers['content-type'] })
    
    // 创建URL
    const url = window.URL.createObjectURL(blob)
    
    // 创建下载链接并触发点击
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${currentClass.className}学生名单.xlsx`
    document.body.appendChild(a)
    a.click()
    
    // 清理
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出学生名单失败:', error)
    ElMessage.error('导出学生名单失败')
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchClassList()
}

// 重置搜索
const resetSearch = () => {
  searchText.value = ''
  currentPage.value = 1
  fetchClassList()
}

// 刷新表格
const refreshTable = () => {
  fetchClassList()
}

// 改变页码大小
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchClassList()
}

// 改变当前页
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchClassList()
}

// 获取班级人数标签类型
const getCountTagType = (count) => {
  if (count > 30) return 'success'
  if (count > 15) return 'warning'
  if (count > 0) return 'info'
  return 'danger'
}

// 获取积分样式
const getPointsClass = (points) => {
  if (!points) return ''
  if (points > 120) return 'points-high'
  if (points < 80) return 'points-low'
  return 'points-normal'
}

// 获取学生状态标签类型
const getStudentStatusType = (status) => {
  if (status === 0) return 'success'
  if (status === 1) return 'warning'
  return 'danger'
}

// 获取学生状态文本
const getStudentStatusText = (status) => {
  if (status === 0) return '正常'
  if (status === 1) return '休学'
  if (status === 2) return '退学'
  return '未知'
}

// 日期格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    // 处理日期格式
    let date
    if (typeof dateString === 'string' && dateString.includes('T')) {
      // ISO格式日期
      date = new Date(dateString)
    } else if (typeof dateString === 'number') {
      // 时间戳
      date = new Date(dateString)
    } else if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // YYYY-MM-DD格式
      const [year, month, day] = dateString.split('-').map(Number)
      date = new Date(year, month - 1, day)
    } else {
      // 其他格式尝试直接解析
      date = new Date(dateString)
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期:', dateString)
      return '-'
    }
    
    return format(date, 'yyyy-MM-dd')
  } catch (e) {
    console.error('格式化日期出错:', e, dateString)
    return '-'
  }
}

// 初始化
onMounted(async () => {
  try {
    // 先获取用户选项，以便在获取班级列表时能正确显示讲师和导员
    await fetchUserOptions()
    await fetchStageOptions()
    await fetchClassList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败，请刷新页面重试')
  }
})
</script>

<style scoped>
.class-management-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background: #f8fafc;
}

/* 隐藏WebKit浏览器的滚动条 */
.class-management-container::-webkit-scrollbar {
  display: none;
}

.filter-card, .table-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: visible;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: white;
  border: 1px solid #e5e7eb;
  animation: slideInUp 0.5s ease-out;
}

.filter-card:hover, .table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0 16px 0;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  font-size: 18px;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.total-info {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.class-statistics {
  margin-top: 20px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.points-high {
  color: #10b981;
  font-weight: 600;
}

.points-low {
  color: #ef4444;
  font-weight: 600;
}

.points-normal {
  color: #3b82f6;
  font-weight: 500;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

:deep(.el-table th) {
  background: #f9fafb;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafafa;
}

:deep(.el-table__body tr:hover > td) {
  background: #f0f9ff !important;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  font-size: 12px;
}

:deep(.el-tag--success) {
  background: #dcfce7;
  color: #166534;
}

:deep(.el-tag--info) {
  background: #e0e7ff;
  color: #3730a3;
}

:deep(.el-tag--warning) {
  background: #fef3c7;
  color: #92400e;
}

:deep(.el-tag--danger) {
  background: #fee2e2;
  color: #991b1b;
}

/* 按钮样式优化 */
:deep(.el-button--text) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.el-button--text:hover) {
  background: #f3f4f6;
}

/* 表单组件样式优化 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #d1d5db;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-date-editor .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.el-textarea__inner:hover) {
  border-color: #d1d5db;
}

:deep(.el-textarea__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

:deep(.el-dialog__header) {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: #1f2937;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .class-management-container {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
    gap: 12px;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .filter-card, .table-card {
    border-radius: 8px;
    margin-bottom: 16px;
  }
}
</style>
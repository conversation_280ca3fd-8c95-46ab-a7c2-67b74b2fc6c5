package com.zhentao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.pojo.PointsApply;
import com.zhentao.utils.Result;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【points_apply】的数据库操作Service
 * @createDate 2025-07-07 18:59:20
 */
public interface PointsApplyService extends IService<PointsApply> {

    Result addJiFen(@RequestBody PointsApply pointsApply);

    Result listPointsApplies(Map<String, Object> params);

    /**
     * 获取积分历史记录，支持多种过滤条件
     * @param params 过滤参数
     * @return 积分历史记录列表
     */
    Result getPointsHistory(PointsApply pointsApply);

    /**
     * 获取积分统计信息
     * @param params 过滤参数
     * @return 积分统计信息
     */
    Result getPointsStatistics(Map<String, Object> params);

    /**
     * 撤销积分申请
     * @param applyId 申请ID
     * @return 操作结果
     */
    Result cancelPointsApplication(Integer applyId);

    /**
     * 查询当天加加分记录
     */
    public Result queryTodayAddPoints();
    /**
     * 查询当天的减分记录
     */
    public Result queryTodayMinusPoints();


}

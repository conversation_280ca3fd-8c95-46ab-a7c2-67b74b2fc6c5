<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import {banji, DYlist1, GZRlist1, JSlist1, YZlist1, YZRlist1,} from "@/api/shenpi.js";
const pagedTableData=ref([])
// 筛选表单
const filterForm = reactive({
  type: '',
  status2: '',
  studentInfo: '',
  className: '',
  dateRange: []
})
const list1Select=ref({
  pointsChange:"",
  classId:"",
  status2:"",
  pageNum:1,
  pageSize:5
})
const classList=ref([])
function getClass(){
  banji().then(res=>{
    classList.value=res.data.data
  })
}getClass()
const role=localStorage.getItem("roleId")
function getlist1(){
  if (role.includes("1")){
    YZlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role.includes("3")&&role.includes("4")){
    GZRlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if(role.includes("3")&&role.includes("5")){
    GZRlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role.includes("2")&&role.includes("4")){
    YZRlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role.includes("2")&&role.includes("5")){
    YZRlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role=="4"){
    JSlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role=="5"){
    DYlist1(list1Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }
}
getlist1()
// // 班级选项
// const classOptions = [
//   '计算机科学1班',
//   '计算机科学2班',
//   '软件工程1班',
//   '软件工程2班',
//   '信息安全1班',
//   '数据科学1班',
//   '人工智能1班'
// ]

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref({})

// 计算属性
const filteredTableData = computed(() => {
  let result = tableData.value
  
  // 按申请类型筛选
  if (filterForm.type) {
    result = result.filter(item => item.type === filterForm.type)
  }
  
  // 按处理结果筛选
  if (filterForm.status) {
    result = result.filter(item => item.status === filterForm.status)
  }
  
  // 按学生信息筛选
  if (filterForm.studentInfo) {
    result = result.filter(item => 
      item.studentId.includes(filterForm.studentInfo) || 
      item.studentName.includes(filterForm.studentInfo)
    )
  }
  
  // 按班级筛选
  if (filterForm.className) {
    result = result.filter(item => item.className === filterForm.className)
  }
  
  // 按日期范围筛选
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const startDate = new Date(filterForm.dateRange[0])
    const endDate = new Date(filterForm.dateRange[1])
    endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间
    
    result = result.filter(item => {
      const itemDate = new Date(item.processTime)
      return itemDate >= startDate && itemDate <= endDate
    })
  }
  
  return result
})

// const pagedTableData = computed(() => {
//   const start = (currentPage.value - 1) * pageSize.value
//   const end = start + pageSize.value
//   return filteredTableData.value.slice(start, end)
// })

// // 生命周期钩子
// onMounted(() => {
//   loadData()
// })

// 方法定义
const handleSearch = () => {
  currentPage.value = 1
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'dateRange') {
      filterForm[key] = []
    } else {
      filterForm[key] = ''
    }
  })
  currentPage.value = 1
}

// const loadData = () => {
//   loading.value = true
//   // 模拟加载数据
//   setTimeout(() => {
//     tableData.value = generateMockData()
//     loading.value = false
//   }, 500)
// }

// const refreshData = () => {
//   loadData()
// }
const total=ref()
const handleSizeChange = (val) => {
  getlist1()
}

const handleCurrentChange = (val) => {
  getlist1()
}
const xq=ref([])
const viewDetail = (row) => {
  xq.value=row
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
}

const getDetailTitle = (detail) => {
  if (!detail.type) return '申请详情'
  return detail.type === 'add' ? '积分添加申请' : '积分扣除申请'
}

// 生成模拟数据
// const generateMockData = () => {
//   const data = []
//   const students = [
//     { id: '2020001', name: '张三', className: '计算机科学1班' },
//     { id: '2020002', name: '李四', className: '软件工程2班' },
//     { id: '2020003', name: '王五', className: '信息安全1班' },
//     { id: '2020004', name: '赵六', className: '计算机科学1班' },
//     { id: '2020005', name: '钱七', className: '软件工程2班' }
//   ]
//
//   const addTypes = [
//     { id: 1, name: '期末考试优秀', points: 10 },
//     { id: 2, name: '获得学科竞赛奖项', points: 15 },
//     { id: 3, name: '完成高质量课程作业', points: 5 },
//     { id: 4, name: '积极参与课堂讨论', points: 3 },
//     { id: 5, name: '参与志愿服务', points: 8 }
//   ]
//
//   const deductTypes = [
//     { id: 6, name: '课堂迟到', points: 2 },
//     { id: 7, name: '课堂缺勤', points: 5 },
//     { id: 8, name: '课堂扰乱秩序', points: 3 },
//     { id: 9, name: '宿舍卫生不合格', points: 3 },
//     { id: 10, name: '考试作弊', points: 20 }
//   ]
//
//   const applicants = ['王老师', '李辅导', '张主任', '陈院长']
//   const approvers = ['刘院长', '周主任', '吴教授', '郑主任']
//
//   // 生成30条模拟数据
//   for (let i = 0; i < 30; i++) {
//     const student = students[Math.floor(Math.random() * students.length)]
//     const type = Math.random() > 0.5 ? 'add' : 'deduct'
//     const typeList = type === 'add' ? addTypes : deductTypes
//     const typeInfo = typeList[Math.floor(Math.random() * typeList.length)]
//     const applicant = applicants[Math.floor(Math.random() * applicants.length)]
//     const approver = approvers[Math.floor(Math.random() * approvers.length)]
//     const status = Math.random() > 0.3 ? 'approved' : 'rejected'
//
//     // 生成随机申请日期（最近60天内）
//     const createDate = new Date()
//     createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 60))
//
//     // 生成随机处理日期（申请日期之后的1-5天）
//     const processDate = new Date(createDate)
//     processDate.setDate(processDate.getDate() + Math.floor(Math.random() * 5) + 1)
//
//     const record = {
//       id: i + 1,
//       type: type,
//       studentId: student.id,
//       studentName: student.name,
//       className: student.className,
//       points: typeInfo.points,
//       typeName: typeInfo.name,
//       reason: type === 'add' ?
//         `因${typeInfo.name}表现优秀，特此申请加分。` :
//         `因${typeInfo.name}，根据规定申请扣分。`,
//       createTime: formatDateTime(createDate),
//       processTime: formatDateTime(processDate),
//       applicantName: applicant,
//       approverName: approver,
//       status: status,
//       comment: status === 'approved' ?
//         '申请材料齐全，符合加分/扣分条件，同意此申请。' :
//         '申请材料不足，不符合加分/扣分条件，拒绝此申请。',
//       attachments: Math.random() > 0.7 ? [
//         {
//           name: '证明材料1.jpg',
//           url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
//         }
//       ] : []
//     }
//
//     data.push(record)
//   }
//
//   // 按处理时间降序排序，最新处理的记录在前面
//   return data.sort((a, b) => new Date(b.processTime) - new Date(a.processTime))
// }

// 格式化日期时间 YYYY-MM-DD HH:MM:SS
const formatDateTime = (date) => {
  if (!date) return ''
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
</script>

<template>
  <div class="approval-processed-container">
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>已处理申请</h3>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="list1Select" :inline="true" class="filter-form">
        <el-form-item label="申请类型">
          <el-select v-model="list1Select.pointsChange" placeholder="全部类型" @change="getlist1">
            <el-option label="积分增加" value=1 >积分增加</el-option>
            <el-option label="积分扣除" value=2 >积分扣除</el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="最终处理结果">
          <el-select v-model="list1Select.status2" placeholder="请选择结果" clearable style="width: 120px" @change="getlist1">
            <el-option label="待审核" value=1>待审核</el-option>
            <el-option label="已通过" value=2>已通过</el-option>
            <el-option label="已拒绝" value=3>已拒绝</el-option>
          </el-select>
        </el-form-item>
        
<!--        <el-form-item label="学生信息">-->
<!--          <el-input-->
<!--            v-model="filterForm.studentInfo"-->
<!--            placeholder="学号/姓名"-->
<!--            clearable-->
<!--            style="width: 180px"-->
<!--          ></el-input>-->
<!--        </el-form-item>-->
<!--        -->
        <el-form-item label="班级">
          <el-select v-model="list1Select.classId" placeholder="请选择班级" style="width: 180px" @change="getlist1">
            <el-option v-for="item in classList" :key="item.className" :label="item.className" :value="item.classId"></el-option>
          </el-select>
        </el-form-item>
        
<!--        <el-form-item label="处理时间">-->
<!--          <el-date-picker-->
<!--            v-model="filterForm.dateRange"-->
<!--            type="daterange"-->
<!--            range-separator="至"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--            style="width: 320px"-->
<!--          ></el-date-picker>-->
<!--        </el-form-item>-->
        
        <el-form-item>
          <el-button type="primary" @click="getlist1">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="table-card" shadow="hover">
      <el-table :data="pagedTableData" style="width: 100%" v-loading="loading" border stripe>
        <el-table-column type="index" label="序号" width="120" />
        <el-table-column #default="scope" label="学生姓名" width="120" >
          {{scope.row.student.realName}}
        </el-table-column>
        <el-table-column #default="scope" label="班级" width="120" >
          <span v-if="scope.row.eduClass!=null">{{scope.row.eduClass.className}}</span>
        </el-table-column>
        <el-table-column #default="scope" label="申请人" width="120" >
          <span v-if="scope.row.applyUser!=null"> {{scope.row.applyUser.username}}</span>
        </el-table-column>
        <el-table-column #default="scope" label="执行操作" width="120" >
          <span v-if="scope.row.pointsChange==1" style="color: green">加分</span>
          <span v-if="scope.row.pointsChange==2" style="color: red">减分</span>
        </el-table-column>
        <el-table-column prop="points" label="分值" width="120" />
        <el-table-column prop="reason" label="申请理由" width="120" />
        <el-table-column #default="scope" label="图片" width="120" >
          <el-image style="width: 100px; height: 100px" :src="scope.row.evidenceImages" />
        </el-table-column>
        <el-table-column #default="scope" label="导员讲师审批状态" width="120">
          <span v-if="scope.row.status==1">待审核</span>
          <span v-if="scope.row.status==2">已通过</span>
          <span v-if="scope.row.status==3">已拒绝</span>
        </el-table-column>
        <el-table-column #default="scope" label="主任审批状态" width="120">
          <span v-if="scope.row.status1==1">待审核</span>
          <span v-if="scope.row.status1==2">已通过</span>
          <span v-if="scope.row.status1==3">已拒绝</span>
        </el-table-column>
        <el-table-column #default="scope" label="院长审批状态" width="120">
          <span v-if="scope.row.status2==1">待审核</span>
          <span v-if="scope.row.status2==2">已通过</span>
          <span v-if="scope.row.status2==3">已拒绝</span>
        </el-table-column>
        <el-table-column #default="scope" label="审核人" width="120" >
          <span v-if="scope.row.reviewer!=null">  {{scope.row.reviewer.username}}</span>
        </el-table-column>
        <el-table-column prop="reviewTime" label="审核时间" width="120" />
        <el-table-column prop="reviewComment" label="审核意见" width="120" />
        <el-table-column #default="scope" label="创建人" width="120" >
          <span v-if="scope.row.createUser!=null"> {{scope.row.createUser.username}}</span>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column #default="scope" label="修改人" width="120" >
          <span v-if="scope.row.updateUser!=null">{{scope.row.updateUser.username}}</span>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="120" />
        <el-table-column prop="remark" label="备注" width="120" />
        <el-table-column #default="scope" label="删除标志" width="120" >
          <span v-if="scope.row.delFlag==1">存在</span>
          <span v-if="scope.row.delFlag==2">删除</span>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
            v-model:current-page="list1Select.pageNum"
            v-model:page-size="list1Select.pageSize"
            :page-sizes="[5, 10, 15, 20]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible"  width="600px" title="申请详情">
      <el-form :model="xq" label-width="auto" style="max-width: 600px">
        <el-form-item label="学生姓名">
          <el-input v-model="xq.student.realName" />
        </el-form-item>
        <el-form-item label="班级">
          <el-input v-model="xq.eduClass.className" />
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model="xq.applyUser.username" />
        </el-form-item>
        <el-form-item label="执行操作">
          <span v-if="xq.pointsChange==1">加分</span>
          <span v-if="xq.pointsChange==2">减分</span>
        </el-form-item>
        <el-form-item label="分值">
          <el-input v-model="xq.points" />
        </el-form-item>
        <el-form-item label="申请理由">
          <el-input v-model="xq.reason" />
        </el-form-item>
        <el-form-item label="图片">
          <el-image style="width: 100px; height: 100px" :src="xq.evidenceImages" />
        </el-form-item>
        <el-form-item label="状态">
          <span v-if="xq.status==1">待审核</span>
          <span v-if="xq.status==2">已通过</span>
          <span v-if="xq.status==3">已拒绝</span>
        </el-form-item>
        <el-form-item label="审核人" >
          <span v-if="xq.reviewer!=null">
          <el-input v-model="xq.reviewer.username" />
          </span>
        </el-form-item>
        <el-form-item label="审核时间">
          <span v-if="xq.reviewer!=null">
          <el-input v-model="xq.reviewTime" />
          </span>
        </el-form-item>
        <el-form-item label="创建人"  >
          <span v-if="xq.createUser!=null">
          <el-input v-model="xq.createUser.username" />
          </span>
        </el-form-item>
        <el-form-item label="创建时间" >
          <span v-if="xq.createUser!=null">
          <el-input v-model="xq.createTime" />
          </span>
        </el-form-item>
        <el-form-item label="更新人" >
          <span v-if="xq.updateUser!=null">
          <el-input v-model="xq.updateUser.username" />
          </span>
        </el-form-item>
        <el-form-item label="更新时间" >
          <span v-if="xq.updateUser!=null">
          <el-input v-model="xq.updateTime" />
          </span>
        </el-form-item>
        <el-form-item label="删除标志">
          <span v-if="xq.delFlag==1">存在</span>
          <span v-if="xq.delFlag==2">删除</span>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped>
.approval-processed-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.approval-processed-container::-webkit-scrollbar {
  display: none;
}

.filter-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible; /* 改为visible，避免内容被截断 */
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.filter-card:hover, .table-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px; /* 添加底部边距 */
  display: flex;
  justify-content: flex-end;
}

.attachments-container {
  margin-top: 20px;
}

.attachments-container h4 {
  margin-bottom: 10px;
  font-weight: 500;
}

.attachment-image {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}

/* 详情对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 14px;
  box-shadow: 0 8px 32px rgba(60, 120, 200, 0.18);
  background: linear-gradient(135deg, #fafdff 60%, #eaf3fa 100%);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 10px;
  background: linear-gradient(90deg, #3a7bd5, #00d2ff);
  margin-right: 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__headerbtn) {
  top: 18px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

:deep(.el-dialog__body) {
  padding: 24px 32px 10px 32px;
  max-height: 65vh;
  overflow-y: auto;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 10px;
}

:deep(.el-form-item:last-child) {
  border-bottom: none;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #3a7bd5;
  font-size: 15px;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
  background-color: transparent;
}

:deep(.el-input__inner) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-form-item span) {
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
}

:deep(.el-image) {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

:deep(.el-image:hover) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(58, 123, 213, 0.2);
}

@media (max-width: 600px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 10px auto !important;
  }
  
  :deep(.el-dialog__body) {
    padding: 15px !important;
    max-height: 70vh;
  }
  
  :deep(.el-form-item__label) {
    padding-bottom: 8px;
  }
}
</style>
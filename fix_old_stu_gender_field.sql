-- 修复 old_stu 表的 gender 字段长度问题
-- 如果 gender 字段长度不够，需要扩展字段长度

-- 查看当前表结构
-- DESCRIBE old_stu;

-- 方案1：如果 gender 字段长度不够，扩展为 VARCHAR(10)
-- ALTER TABLE old_stu MODIFY COLUMN gender VARCHAR(10) COMMENT '性别（1-男，0-女，9-未知）';

-- 方案2：如果表不存在，创建 old_stu 表
CREATE TABLE IF NOT EXISTS `old_stu` (
  `old_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `oldstu_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学号',
  `stu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学生姓名',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别（1-男，0-女，9-未知）',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `class_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班级名称',
  `points` int(11) NULL DEFAULT 100 COMMENT '综合积分',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-休学，2-退学）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`old_id`) USING BTREE,
  INDEX `idx_oldstu_no` (`oldstu_no`) USING BTREE,
  INDEX `idx_stu_name` (`stu_name`) USING BTREE,
  INDEX `idx_del_flag` (`del_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生历史数据表' ROW_FORMAT = DYNAMIC;

-- 如果需要修改现有表的字段，请根据实际情况选择以下语句之一：

-- 修改 gender 字段为 VARCHAR(10)
-- ALTER TABLE old_stu MODIFY COLUMN gender VARCHAR(10) COMMENT '性别（1-男，0-女，9-未知）';

-- 或者修改为 CHAR(1) 如果只需要存储单个字符
-- ALTER TABLE old_stu MODIFY COLUMN gender CHAR(1) COMMENT '性别（1-男，0-女，9-未知）';

-- 查看修改后的表结构
-- DESCRIBE old_stu;

-- 测试数据插入（可选）
-- INSERT INTO old_stu (oldstu_no, stu_name, gender, class_name, points, status, del_flag) 
-- VALUES ('TEST001', '测试学生', '1', '测试班级', 100, 0, 0);

-- 查看测试数据
-- SELECT * FROM old_stu WHERE oldstu_no = 'TEST001';

-- 删除测试数据
-- DELETE FROM old_stu WHERE oldstu_no = 'TEST001';

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生门户系统测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2em;
            color: white;
        }
        
        .feature-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.4em;
        }
        
        .feature-card p {
            margin: 0 0 20px 0;
            color: #666;
            line-height: 1.6;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #67c23a;
            font-weight: bold;
        }
        
        .tech-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .tech-section h2 {
            margin: 0 0 20px 0;
            color: #333;
            text-align: center;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .tech-category {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .tech-category h4 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }
        
        .tech-tag {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        
        .access-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
        }
        
        .access-section h2 {
            margin: 0 0 20px 0;
        }
        
        .access-info {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .access-info code {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .btn {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #67c23a;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .tech-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 学生门户系统</h1>
            <p>为学生提供全方位的积分管理和学习服务平台</p>
        </div>
        
        <div class="content">
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🏠</div>
                    <h3>首页</h3>
                    <p>个人信息展示和快速导航中心</p>
                    <ul class="feature-list">
                        <li>积分排行榜（实时更新）</li>
                        <li>个人荣誉墙展示</li>
                        <li>通知公告查看</li>
                        <li>快捷操作入口</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">👤</div>
                    <h3>学生明细</h3>
                    <p>全面的个人信息和学习记录管理</p>
                    <ul class="feature-list">
                        <li>个人档案信息管理</li>
                        <li>积分记录查询筛选</li>
                        <li>学习情况统计分析</li>
                        <li>活动参与记录</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📝</div>
                    <h3>申请管理</h3>
                    <p>便捷的在线申请和状态跟踪系统</p>
                    <ul class="feature-list">
                        <li>在线提交加分申请</li>
                        <li>申请扣除积分操作</li>
                        <li>申请记录状态跟踪</li>
                        <li>证明材料上传</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">ℹ️</div>
                    <h3>关于我们</h3>
                    <p>了解系统信息和开发团队</p>
                    <ul class="feature-list">
                        <li>系统功能介绍</li>
                        <li>开发团队展示</li>
                        <li>联系方式提供</li>
                        <li>版本信息更新</li>
                    </ul>
                </div>
            </div>
            
            <div class="tech-section">
                <h2>🛠️ 技术架构</h2>
                <div class="tech-grid">
                    <div class="tech-category">
                        <h4>前端技术</h4>
                        <div class="tech-tags">
                            <span class="tech-tag">Vue 3</span>
                            <span class="tech-tag">Element Plus</span>
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">CSS3</span>
                        </div>
                    </div>
                    <div class="tech-category">
                        <h4>后端技术</h4>
                        <div class="tech-tags">
                            <span class="tech-tag">Spring Boot</span>
                            <span class="tech-tag">MyBatis Plus</span>
                            <span class="tech-tag">MySQL</span>
                            <span class="tech-tag">Redis</span>
                        </div>
                    </div>
                    <div class="tech-category">
                        <h4>开发工具</h4>
                        <div class="tech-tags">
                            <span class="tech-tag">IntelliJ IDEA</span>
                            <span class="tech-tag">VS Code</span>
                            <span class="tech-tag">Git</span>
                            <span class="tech-tag">Maven</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="access-section">
                <h2>🚀 系统访问</h2>
                <p><span class="status-indicator"></span>系统运行状态：正常</p>
                
                <div class="access-info">
                    <h3>访问路径</h3>
                    <p>在主系统中访问：<code>/dashboard/student/portal</code></p>
                    <p>完整URL示例：<code>http://localhost:3000/dashboard/student/portal</code></p>
                </div>
                
                <div class="access-info">
                    <h3>使用说明</h3>
                    <p>1. 登录主系统后，在导航菜单中找到"学生门户"</p>
                    <p>2. 点击进入学生门户系统</p>
                    <p>3. 使用顶部导航切换不同功能模块</p>
                    <p>4. 支持桌面端和移动端访问</p>
                </div>
                
                <a href="/dashboard/student/portal" class="btn">立即访问系统</a>
                <a href="src/views/page/studentPageList/README.md" class="btn">查看详细文档</a>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 学生积分管理系统 - 学生门户模块</p>
            <p>为学生服务，让管理更简单</p>
        </div>
    </div>
    
    <script>
        // 简单的页面交互
        document.addEventListener('DOMContentLoaded', function() {
            // 添加卡片点击效果
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // 检查系统状态（模拟）
            setTimeout(() => {
                console.log('学生门户系统组件已加载完成');
                console.log('包含以下组件：');
                console.log('- StudentPortal.vue (主入口)');
                console.log('- HomePage.vue (首页)');
                console.log('- StudentInfoPage.vue (学生明细)');
                console.log('- ApplicationPage.vue (申请管理)');
                console.log('- AboutPage.vue (关于我们)');
            }, 1000);
        });
    </script>
</body>
</html>

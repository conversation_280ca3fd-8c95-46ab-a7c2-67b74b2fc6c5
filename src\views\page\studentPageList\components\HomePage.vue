<template>
  <div class="home-page">
    <!-- 欢迎横幅 -->
    <el-card class="welcome-banner" shadow="hover">
      <div class="banner-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ studentInfo.name }}！</h1>
          <p>学号：{{ studentInfo.studentNo }} | 班级：{{ studentInfo.className }}</p>
        </div>
        <div class="welcome-stats">
          <div class="stat-item">
            <el-icon class="stat-icon"><Trophy /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ studentInfo.currentPoints }}</div>
              <div class="stat-label">当前积分</div>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon"><Star /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ studentInfo.ranking }}</div>
              <div class="stat-label">班级排名</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20" class="main-content">
      <!-- 左侧内容 -->
      <el-col :span="16">
        <!-- 排行榜模块 -->
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>积分排行榜</span>
            </div>
          </template>
          <div class="ranking-content">
            <div class="ranking-tabs">
<!--              <el-radio-group v-model="rankingType" size="small">-->
<!--                <el-radio-button label="class">班级排名</el-radio-button>-->
<!--                <el-radio-button label="grade">年级排名</el-radio-button>-->
<!--              </el-radio-group>-->
            </div>
            <div class="ranking-list">
              <div class="rankings-section">
                <el-row :gutter="20" justify="center">
                  <el-col :xs="24" :sm="12" :md="10" :lg="8">
                    <el-card class="ranking-card">
                      <h3 class="ranking-title">
                        <el-icon class="title-icon"><Trophy /></el-icon>
                        学生积分前10名
                      </h3>
                      <div class="ranking-list">
                        <div
                          v-for="(stu, idx) in topStudents"
                          :key="stu.id"
                          class="ranking-item"
                          :class="{ 'top-three': idx < 3 }"
                        >
                          <div class="rank-number">
                            <el-icon v-if="idx === 0" class="medal-icon gold"><Medal /></el-icon>
                            <el-icon v-else-if="idx === 1" class="medal-icon silver"><Medal /></el-icon>
                            <el-icon v-else-if="idx === 2" class="medal-icon bronze"><Medal /></el-icon>
                            <span v-else class="rank-text">{{ idx + 1 }}</span>
                          </div>
                          <div class="student-info">
                            <div class="student-name">{{ stu.realName }}</div>
                            <div class="student-class">{{ stu.className }}</div>
                          </div>
                          <div class="student-score">{{ stu.points }}分</div>
                        </div>
                      </div>
                    </el-card>
                  </el-col>

                </el-row>
              </div>
<!--              <div-->
<!--                v-for="(student, index) in rankingList"-->
<!--                :key="student.id"-->
<!--                class="ranking-item"-->
<!--                :class="{ 'current-student': student.id === studentInfo.id }"-->
<!--              >-->
<!--                <div class="rank-number">-->
<!--                  <el-icon v-if="index < 3" class="medal-icon" :class="`medal-${index + 1}`">-->
<!--                    <Medal />-->
<!--                  </el-icon>-->
<!--                  <span v-else class="rank-text">{{ index + 1 }}</span>-->
<!--                </div>-->
<!--                <el-avatar :size="40" :src="student.avatar">{{ student.name.charAt(0) }}</el-avatar>-->
<!--                <div class="student-info">-->
<!--                  <div class="student-name">{{ student.name }}</div>-->
<!--                  <div class="student-class">{{ student.className }}</div>-->
<!--                </div>-->
<!--                <div class="student-points">{{ student.points }}分</div>-->
<!--              </div>-->
            </div>
          </div>
        </el-card>

        <!-- 荣誉墙模块 -->
        <el-card class="honor-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Medal /></el-icon>
              <span>荣誉墙</span>
            </div>
          </template>
          <div class="honor-content">
            <div class="honor-categories">
              <el-tag
                v-for="category in honorCategories"
                :key="category.type"
                :type="category.type"
                class="honor-tag"
                @click="selectHonorCategory(category.value)"
              >
                {{ category.label }}
              </el-tag>
            </div>
            <div class="honor-list">
              <div
                v-for="honor in filteredHonors"
                :key="honor.id"
                class="honor-item"
              >
                <el-icon class="honor-icon" :class="honor.iconClass"><Star /></el-icon>
                <div class="honor-info">
                  <div class="honor-title">{{ honor.title }}</div>
                  <div class="honor-desc">{{ honor.description }}</div>
                  <div class="honor-date">{{ honor.date }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="8">
        <!-- 通知公告模块 -->
        <el-card class="notice-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知公告</span>
              <el-link type="primary" :underline="false" @click="viewAllNotices">查看全部</el-link>
            </div>
          </template>
          <div class="notice-content">
            <div
              v-for="notice in noticeList"
              :key="notice.id"
              class="notice-item"
              @click="viewNoticeDetail(notice)"
            >
              <div class="notice-header">
                <el-tag :type="notice.type" size="small">{{ notice.category }}</el-tag>
                <span class="notice-date">{{ notice.date }}</span>
              </div>
              <div class="notice-title">{{ notice.title }}</div>
              <div class="notice-summary">{{ notice.summary }}</div>
            </div>
          </div>
        </el-card>

        <!-- 快捷操作模块 -->
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Operation /></el-icon>
              <span>快捷操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <div
              v-for="action in quickActions"
              :key="action.key"
              class="action-item"
              @click="handleQuickAction(action.key)"
            >
              <el-icon class="action-icon" :class="action.iconClass">
                <component :is="action.icon" />
              </el-icon>
              <div class="action-info">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import {
  Trophy, Star, TrendCharts, Medal, Bell, Operation,
  Document, Plus, Search, Setting
} from '@element-plus/icons-vue'
import {queryTopTenStudents} from "@/api/system/student.js";

// 学生信息
const studentInfo = reactive({
  id: 1,
  name: '张三',
  studentNo: '2021001',
  className: '计算机科学与技术1班',
  currentPoints: 1250,
  ranking: 3
})

// 排行榜类型
const rankingType = ref('class')

// 排行榜数据
const rankingList = ref([
  { id: 2, name: '李四', className: '计算机1班', points: 1380, avatar: '' },
  { id: 3, name: '王五', className: '计算机1班', points: 1320, avatar: '' },
  { id: 1, name: '张三', className: '计算机1班', points: 1250, avatar: '' },
  { id: 4, name: '赵六', className: '计算机1班', points: 1180, avatar: '' },
  { id: 5, name: '钱七', className: '计算机1班', points: 1120, avatar: '' }
])

// 荣誉类别
const honorCategories = ref([
  { label: '全部', value: 'all', type: '' },
  { label: '学习优秀', value: 'study', type: 'success' },
  { label: '活动参与', value: 'activity', type: 'warning' },
  { label: '社会实践', value: 'practice', type: 'info' }
])

const selectedHonorCategory = ref('all')

// 荣誉数据
const honorList = ref([
  {
    id: 1,
    title: '三好学生',
    description: '学习成绩优异，品德良好',
    date: '2024-12-15',
    category: 'study',
    iconClass: 'honor-gold'
  },
  {
    id: 2,
    title: '优秀班干部',
    description: '积极参与班级管理工作',
    date: '2024-11-20',
    category: 'activity',
    iconClass: 'honor-silver'
  },
  {
    id: 3,
    title: '社会实践先进个人',
    description: '暑期社会实践表现突出',
    date: '2024-09-10',
    category: 'practice',
    iconClass: 'honor-bronze'
  }
])
const intervalIds=ref([]);
// mock 排行榜数据
const topStudents = ref([]);
const findTopStudents = () => {
  queryTopTenStudents().then((res) => {
    // 统一字段名
    topStudents.value = res.data.data;
  });
  const id=setInterval(()=>{
    queryTopTenStudents().then((res) => {
      // 统一字段名
      topStudents.value = res.data.data;
    });
  },5000)
  intervalIds.value.push(id)
};
findTopStudents();
// 过滤后的荣誉
const filteredHonors = computed(() => {
  if (selectedHonorCategory.value === 'all') {
    return honorList.value
  }
  return honorList.value.filter(honor => honor.category === selectedHonorCategory.value)
})

// 通知公告数据
const noticeList = ref([
  {
    id: 1,
    title: '关于2024年度奖学金评选的通知',
    summary: '请符合条件的同学及时提交申请材料...',
    category: '重要通知',
    type: 'danger',
    date: '2024-12-20'
  },
  {
    id: 2,
    title: '期末考试安排通知',
    summary: '2024年秋季学期期末考试将于...',
    category: '考试安排',
    type: 'warning',
    date: '2024-12-18'
  },
  {
    id: 3,
    title: '寒假放假时间安排',
    summary: '根据学校安排，寒假放假时间为...',
    category: '放假通知',
    type: 'info',
    date: '2024-12-15'
  }
])

// 快捷操作
const quickActions = ref([
  {
    key: 'apply',
    title: '申请加分',
    description: '提交积分申请',
    icon: Plus,
    iconClass: 'action-primary'
  },
  {
    key: 'query',
    title: '查询记录',
    description: '查看积分记录',
    icon: Search,
    iconClass: 'action-success'
  },
  {
    key: 'application',
    title: '我的申请',
    description: '管理申请状态',
    icon: Document,
    iconClass: 'action-warning'
  },
  {
    key: 'settings',
    title: '个人设置',
    description: '修改个人信息',
    icon: Setting,
    iconClass: 'action-info'
  }
])

// 方法
const selectHonorCategory = (category) => {
  selectedHonorCategory.value = category
}

const viewAllNotices = () => {
  console.log('查看全部通知')
}

const viewNoticeDetail = (notice) => {
  console.log('查看通知详情:', notice)
}

const handleQuickAction = (actionKey) => {
  console.log('执行快捷操作:', actionKey)
}
</script>

<style scoped>
.home-page {
  padding: 0;
}

/* 欢迎横幅 */
.welcome-banner {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-banner :deep(.el-card__body) {
  padding: 30px;
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.welcome-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 32px;
  color: #ffd700;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 主要内容 */
.main-content {
  margin-top: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 排行榜区域 */
.ranking-tabs {
  margin-bottom: 20px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
  cursor: pointer;
}

.ranking-item:hover {
  background: #f5f7fa;
}

.ranking-item.current-student {
  background: #e3f2fd;
  border: 1px solid #2196f3;
}
.rankings-section {
  max-width: 1200px;
  margin: 40px auto 0 auto;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
/* 排行榜卡片样式 */
.ranking-card {
  margin: 0 auto 40px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(60, 154, 240, 0.12);
  min-height: 500px;
  width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  border: 1px solid rgba(64, 158, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.ranking-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(60, 154, 240, 0.18);
}

.ranking-title {
  text-align: center;
  font-size: 22px;
  color: #1a4f8c;
  margin-bottom: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px 20px 0;
  background: linear-gradient(135deg, #409eff, #1a4f8c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  font-size: 24px;
  color: #ffd700;
}

.ranking-list {
  padding: 0 20px 20px;
  margin: 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #f0f2f7;
  transition: all 0.3s ease;
  position: relative;
}

.ranking-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  border-color: #409eff;
}

.ranking-item.top-three {
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
  border-color: #ffd700;
}

.ranking-item.top-three:hover {
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
}

.rank-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  border-radius: 50%;
  background: #f5f7fa;
  font-weight: bold;
  font-size: 16px;
  color: #606266;
}

.rank-text {
  font-size: 16px;
  font-weight: 700;
}

.medal-icon {
  font-size: 24px;
}

.medal-icon.gold {
  color: #ffd700;
}

.medal-icon.silver {
  color: #c0c0c0;
}

.medal-icon.bronze {
  color: #cd7f32;
}

.student-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.student-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.student-class {
  font-size: 13px;
  color: #909399;
  line-height: 1.2;
}

.student-score {
  font-size: 16px;
  font-weight: 700;
  color: #67c23a;
  min-width: 60px;
  text-align: right;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ranking-card {
    margin-bottom: 20px;
    min-height: 400px;
  }

  .ranking-title {
    font-size: 18px;
    padding: 15px 15px 0;
  }

  .ranking-list {
    padding: 0 15px 15px;
  }

  .ranking-item {
    padding: 10px 12px;
    margin-bottom: 6px;
  }

  .rank-number {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }

  .medal-icon {
    font-size: 20px;
  }

  .student-name {
    font-size: 14px;
  }

  .student-class {
    font-size: 12px;
  }

  .student-score {
    font-size: 14px;
    min-width: 50px;
  }
}
.rank-number {
  width: 40px;
  text-align: center;
  margin-right: 12px;
}

.medal-icon {
  font-size: 24px;
}

.medal-1 { color: #ffd700; }
.medal-2 { color: #c0c0c0; }
.medal-3 { color: #cd7f32; }

.rank-text {
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

.student-info {
  flex: 1;
  margin-left: 12px;
}

.student-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.student-class {
  font-size: 12px;
  color: #666;
}

.student-points {
  font-weight: bold;
  color: #2196f3;
}

/* 荣誉墙卡片 */
.honor-categories {
  margin-bottom: 16px;
}

.honor-tag {
  margin-right: 8px;
  cursor: pointer;
}

.honor-list {
  max-height: 300px;
  overflow-y: auto;
}

.honor-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #fafafa;
}

.honor-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.honor-gold { color: #ffd700; }
.honor-silver { color: #c0c0c0; }
.honor-bronze { color: #cd7f32; }

.honor-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.honor-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.honor-date {
  font-size: 12px;
  color: #999;
}

/* 通知公告卡片 */
.notice-card {
  margin-bottom: 20px;
}

.notice-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.notice-item:hover {
  background: #f0f0f0;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notice-date {
  font-size: 12px;
  color: #999;
}

.notice-title {
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.4;
}

.notice-summary {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 快捷操作卡片 */
.quick-actions {
  display: grid;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 24px;
  margin-right: 12px;
}

.action-primary { color: #409eff; }
.action-success { color: #67c23a; }
.action-warning { color: #e6a23c; }
.action-info { color: #909399; }

.action-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-stats {
    justify-content: center;
  }
}
</style>

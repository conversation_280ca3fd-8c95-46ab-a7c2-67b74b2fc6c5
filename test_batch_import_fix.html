<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量导入修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 批量导入修复效果测试</h1>
        <p><strong>问题描述：</strong>实现 /batch/import 方法后，其他所有方法的响应信息都是失败的提示</p>
        <p><strong>修复方案：</strong>修改 MyBatis-Plus 执行器配置，添加连接状态重置机制</p>
    </div>

    <div class="container">
        <h2>🧪 测试步骤</h2>
        
        <div class="test-section">
            <h3>1. 测试基础API功能</h3>
            <p>验证常用的学生管理API是否正常工作</p>
            <button class="test-button" onclick="testBasicAPIs()">测试基础API</button>
            <div id="basic-api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 模拟批量导入操作</h3>
            <p>执行批量导入操作，观察是否影响其他API</p>
            <button class="test-button" onclick="simulateBatchImport()">模拟批量导入</button>
            <div id="batch-import-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试批量导入后的API状态</h3>
            <p>验证批量导入完成后，其他API是否仍然正常</p>
            <button class="test-button" onclick="testAPIsAfterBatch()" id="test-after-batch" disabled>测试导入后API</button>
            <div id="after-batch-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 数据库连接状态检查</h3>
            <p>检查数据库连接池和执行器状态</p>
            <button class="test-button" onclick="checkDatabaseStatus()">检查数据库状态</button>
            <div id="database-status-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 测试结果总览</h2>
        <div id="test-summary">
            <p>请按顺序执行上述测试...</p>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let testResults = {
            basicAPI: null,
            batchImport: null,
            afterBatch: null,
            databaseStatus: null
        };

        // 获取认证token
        function getAuthToken() {
            return localStorage.getItem('Authorization') || '';
        }

        // 通用API请求函数
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': getAuthToken()
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            try {
                const response = await fetch(API_BASE + url, finalOptions);
                const data = await response.json();
                return {
                    success: response.ok && data.code === 200,
                    data: data,
                    status: response.status,
                    statusText: response.statusText
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    data: null
                };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
        }

        // 更新测试总览
        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            let html = '<h3>测试结果：</h3>';
            
            Object.entries(testResults).forEach(([key, result]) => {
                const status = result === null ? 'info' : (result ? 'success' : 'error');
                const statusText = result === null ? '未测试' : (result ? '通过' : '失败');
                const statusClass = `status-${status}`;
                
                html += `<p><span class="status-indicator ${statusClass}"></span>${getTestName(key)}: ${statusText}</p>`;
            });
            
            summary.innerHTML = html;
        }

        function getTestName(key) {
            const names = {
                basicAPI: '基础API测试',
                batchImport: '批量导入测试',
                afterBatch: '导入后API测试',
                databaseStatus: '数据库状态检查'
            };
            return names[key] || key;
        }

        // 测试基础API
        async function testBasicAPIs() {
            showResult('basic-api-result', '正在测试基础API...', 'info');
            
            const tests = [
                { name: '获取班级选项', url: '/edu-student/getClassOptions', method: 'GET' },
                { name: '学生分页查询', url: '/edu-student/findPage', method: 'POST', body: { pageNum: 1, pageSize: 10 } },
                { name: '班级分页查询', url: '/edu-class/findPage', method: 'POST', body: { pageNum: 1, pageSize: 10 } }
            ];

            let results = [];
            let allPassed = true;

            for (const test of tests) {
                try {
                    const options = {
                        method: test.method,
                        ...(test.body && { body: JSON.stringify(test.body) })
                    };
                    
                    const result = await apiRequest(test.url, options);
                    const passed = result.success;
                    allPassed = allPassed && passed;
                    
                    results.push(`${test.name}: ${passed ? '✅ 通过' : '❌ 失败'} (${result.status || 'N/A'})`);
                    if (!passed) {
                        results.push(`  错误: ${result.error || result.data?.message || '未知错误'}`);
                    }
                } catch (error) {
                    allPassed = false;
                    results.push(`${test.name}: ❌ 异常 - ${error.message}`);
                }
            }

            testResults.basicAPI = allPassed;
            showResult('basic-api-result', results.join('\n'), allPassed ? 'success' : 'error');
            updateTestSummary();
        }

        // 模拟批量导入
        async function simulateBatchImport() {
            showResult('batch-import-result', '正在模拟批量导入...', 'info');
            
            // 模拟批量导入数据
            const importData = {
                classes: [
                    {
                        className: "测试班级2024",
                        counselor: "测试辅导员",
                        teacher: "测试讲师",
                        classroom: "测试教室",
                        stageName: "测试阶段",
                        courseName: "测试课程",
                        students: [
                            { studentNo: "TEST001", realName: "测试学生1" },
                            { studentNo: "TEST002", realName: "测试学生2" }
                        ]
                    }
                ]
            };

            try {
                const result = await apiRequest('/edu-student/batch/import', {
                    method: 'POST',
                    body: JSON.stringify(importData)
                });

                testResults.batchImport = result.success;
                
                if (result.success) {
                    showResult('batch-import-result', 
                        `✅ 批量导入成功\n响应数据: ${JSON.stringify(result.data, null, 2)}`, 
                        'success');
                    document.getElementById('test-after-batch').disabled = false;
                } else {
                    showResult('batch-import-result', 
                        `❌ 批量导入失败\n错误: ${result.error || result.data?.message || '未知错误'}\n响应: ${JSON.stringify(result.data, null, 2)}`, 
                        'error');
                }
            } catch (error) {
                testResults.batchImport = false;
                showResult('batch-import-result', `❌ 批量导入异常: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }

        // 测试批量导入后的API状态
        async function testAPIsAfterBatch() {
            showResult('after-batch-result', '正在测试批量导入后的API状态...', 'info');
            
            // 等待一秒确保批量导入完全完成
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const tests = [
                { name: '学生分页查询', url: '/edu-student/findPage', method: 'POST', body: { pageNum: 1, pageSize: 10 } },
                { name: '添加学生测试', url: '/edu-student/addStu', method: 'POST', body: { 
                    studentNo: 'TEST_AFTER_' + Date.now(), 
                    realName: '导入后测试学生',
                    classId: 1,
                    gender: 1
                }},
                { name: '班级分页查询', url: '/edu-class/findPage', method: 'POST', body: { pageNum: 1, pageSize: 10 } }
            ];

            let results = [];
            let allPassed = true;

            for (const test of tests) {
                try {
                    const options = {
                        method: test.method,
                        ...(test.body && { body: JSON.stringify(test.body) })
                    };
                    
                    const result = await apiRequest(test.url, options);
                    const passed = result.success;
                    allPassed = allPassed && passed;
                    
                    results.push(`${test.name}: ${passed ? '✅ 通过' : '❌ 失败'} (${result.status || 'N/A'})`);
                    if (!passed) {
                        results.push(`  错误: ${result.error || result.data?.message || '未知错误'}`);
                    }
                } catch (error) {
                    allPassed = false;
                    results.push(`${test.name}: ❌ 异常 - ${error.message}`);
                }
            }

            testResults.afterBatch = allPassed;
            showResult('after-batch-result', results.join('\n'), allPassed ? 'success' : 'error');
            updateTestSummary();
        }

        // 检查数据库状态
        async function checkDatabaseStatus() {
            showResult('database-status-result', '正在检查数据库状态...', 'info');
            
            const checks = [
                { name: '数据库连接测试', url: '/edu-student/getClassOptions', method: 'GET' },
                { name: '简单查询测试', url: '/edu-student/findPage', method: 'POST', body: { pageNum: 1, pageSize: 1 } }
            ];

            let results = [];
            let allPassed = true;

            for (const check of checks) {
                try {
                    const startTime = Date.now();
                    const options = {
                        method: check.method,
                        ...(check.body && { body: JSON.stringify(check.body) })
                    };
                    
                    const result = await apiRequest(check.url, options);
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    
                    const passed = result.success;
                    allPassed = allPassed && passed;
                    
                    results.push(`${check.name}: ${passed ? '✅ 正常' : '❌ 异常'} (响应时间: ${responseTime}ms)`);
                    if (!passed) {
                        results.push(`  错误: ${result.error || result.data?.message || '未知错误'}`);
                    }
                } catch (error) {
                    allPassed = false;
                    results.push(`${check.name}: ❌ 异常 - ${error.message}`);
                }
            }

            testResults.databaseStatus = allPassed;
            showResult('database-status-result', results.join('\n'), allPassed ? 'success' : 'warning');
            updateTestSummary();
        }

        // 初始化
        updateTestSummary();
    </script>
</body>
</html>

import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { isLoggedIn } from '@/utils/userUtils.js'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue'),
      meta: { title: '登录', requiresAuth: false }
    },
    {
      // Landing page as the default route
      path: '/',
      name: 'landing',
      component: () => import('../views/LandingPage.vue'),
      meta: { title: '振涛教育云计算学院', requiresAuth: false }
    },
    {
      path: '/dashboard',
      component: () => import('../components/index.vue'),
      // redirect: '/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'dashboard',
          component: () => import('../views/page/HomePage.vue'),
          meta: { title: '首页', requiresAuth: true }
        },
        // 学生管理
        {
          path: 'student/list',
          name: 'studentList',
          component: () => import('../views/page/student/StudentList.vue'),
          meta: { title: '学生列表', requiresAuth: true }
        },
        {
          path: 'student/class',
          name: 'classList',
          component: () => import('../views/page/student/ClassManagement.vue'),
          meta: { title: '班级管理', requiresAuth: true }
        },
        {
          path: 'student/imp',
          name: 'imp',
          component: () => import('../views/page/student/StudentImport.vue'),
          meta: { title: '班级管理', requiresAuth: true }
        },
        {
          path: 'student/import',
          name: 'studentImport',
          component: () => import('../views/page/student/BatchImport.vue'),
          meta: { title: '导入学生', requiresAuth: true }
        },
        {
          path: 'student/imports',
          name: 'studentImports',
          component: () => import('../views/page/student/BatchImportList_fixed.vue'),
          meta: { title: '批量导入', requiresAuth: true }
        },
        // 积分管理
        {
          path: 'points/add',
          name: 'pointsAdd',
          component: () => import('../views/page/points/PointsAdd.vue'),
          meta: { title: '积分增加', requiresAuth: true }
        },
        {
          path: 'points/deduct',
          name: 'pointsDeduct',
          component: () => import('../views/page/points/PointsDeduct.vue'),
          meta: { title: '积分扣除', requiresAuth: true }
        },
        {
          path: 'points/history',
          name: 'pointsHistory',
          component: () => import('../views/page/points/PointsHistory.vue'),
          meta: { title: '积分历史', requiresAuth: true }
        },
        {
          path: 'points/rules',
          name: 'pointsRules',
          component: () => import('../views/page/points/PointsRules.vue'),
          meta: { title: '积分规则', requiresAuth: true }
        },
        {
          path: 'points/activity',
          name: 'pointsActivity',
          component: () => import('../views/page/points/ActivityManage.vue'),
          meta: { title: '活动管理', requiresAuth: true }
        },
        {
          path: 'points/application',
          name: 'pointsApplication',
          component: () => import('../views/page/points/ActivityApplicationManage.vue'),
          meta: { title: '报名管理', requiresAuth: true }
        },
        // 申请审批
        {
          path: 'approval/pending',
          name: 'approvalPending',
          component: () => import('../views/page/approval/ApprovalPending.vue'),
          meta: { title: '待审批', requiresAuth: true }
        },
        {
          path: 'approval/processed',
          name: 'approvalProcessed',
          component: () => import('../views/page/approval/ApprovalProcessed.vue'),
          meta: { title: '已处理', requiresAuth: true }
        },
        {
          path: 'approval/my',
          name: 'approvalMy',
          component: () => import('../views/page/approval/ApprovalMy.vue'),
          meta: { title: '我的申请', requiresAuth: true }
        },
        // 统计分析
        {
          path: 'statistics/student',
          name: 'statisticsStudent',
          component: () => import('../views/page/statistics/StudentStatistics.vue'),
          meta: { title: '学生积分分析', requiresAuth: true }
        },
        {
          path: 'statistics/class',
          name: 'statisticsClass',
          component: () => import('../views/page/statistics/ClassStatistics.vue'),
          meta: { title: '班级积分分析', requiresAuth: true }
        },
        {
          path: 'statistics/trend',
          name: 'statisticsTrend',
          component: () => import('../views/page/statistics/TrendAnalysis.vue'),
          meta: { title: '积分趋势分析', requiresAuth: true }
        },
        {
          path: 'statistics/export',
          name: 'statisticsExport',
          component: () => import('../views/page/statistics/DataExport.vue'),
          meta: { title: '数据导出', requiresAuth: true }
        },
        // 系统设置
        {
          path: 'settings/user',
          name: 'settingsUser',
          component: () => import('../views/page/settings/UserManage.vue'),
          meta: { title: '用户管理', requiresAuth: true }
        },
        {
          path: 'settings/role',
          name: 'settingsRole',
          component: () => import('../views/page/settings/RolePermission.vue'),
          meta: { title: '角色权限', requiresAuth: true }
        },
        {
          path: 'settings/system',
          name: 'settingsSystem',
          component: () => import('../views/page/settings/SystemParams.vue'),
          meta: { title: '系统参数', requiresAuth: true }
        },
        {
          path: 'settings/log',
          name: 'settingsLog',
          component: () => import('../views/page/settings/OperationLog.vue'),
          meta: { title: '操作日志', requiresAuth: true }
        },
        {
          path: 'settings/profile',
          name: 'settingsProfile',
          component: () => import('../views/page/settings/UserProfile.vue'),
          meta: { title: '个人信息', requiresAuth: true }
        },
        {
          path: 'settings/password',
          name: 'settingsPassword',
          component: () => import('../views/page/settings/ChangePassword.vue'),
          meta: { title: '修改密码', requiresAuth: true }
        },{
          path: 'log/operation',
          name: 'settingsLog',
          component: () => import('../views/page/log/OperationLog.vue'),
          meta: { title: '操作日志', requiresAuth: true }
        },
        {
          path: 'settings/about',
          name: 'about',
          component: () => import('../views/page/settings/about.vue'),
          meta: { title: '关于我们', requiresAuth: false }
        },
      ]
    },
    // 学生门户
    {
      path: '/student/portal',
      name: 'studentPortal',
      component: () => import('../views/page/studentPageList/StudentPortal.vue'),
      meta: { title: '学生门户', requiresAuth: true }
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      component: () => import('../views/page/NotFound.vue'),
      meta: { title: '页面不存在', requiresAuth: false }
    },
    {
      path: '/points-rules',
      name: 'points-rules',
      component: () => import('../views/page/points-rules.vue'),
      meta: { title: '积分规则', requiresAuth: false }
    },
    {
      path: '/awards',
      name: 'awards',
      component: () => import('../views/page/awards.vue'),
      meta: { title: '荣誉中心', requiresAuth: false }
    },
    {
      path: '/activity-signup',
      name: 'activity-signup',
      component: () => import('../views/page/activity-signup.vue'),
      meta: { title: '热门活动', requiresAuth: false }
    },
    {
      path: '/video-center',
      name: 'video-center',
      component: () => import('../views/page/video-center.vue'),
      meta: { title: '视频中心', requiresAuth: false }
    },

    {
      path: '/teachers',
      name: 'teachers',
      component: () => import('../views/page/teachers.vue'),
      meta: { title: '视频中心', requiresAuth: false }
    },
    {
      path: '/history',
      name: 'history',
      component: () => import('../views/page/history.vue'),
      meta: { title: '视频中心', requiresAuth: false }
    },
    {
      path: '/news',
      name: 'news',
      component: () => import('../views/page/news.vue'),
      meta: { title: '视频中心', requiresAuth: false }
    }
  ],
})


// 全局前置守卫 - 检查用户是否已经登录
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 学生积分管理系统`
  } else {
    document.title = '学生积分管理系统'
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false);

  // 检查用户是否登录
  const loggedIn = isLoggedIn();

  if (requiresAuth && !loggedIn) {
    // 未登录且需要登录的路由，重定向到登录页
    ElMessage.warning('请先登录');
    next('/login');
  } else if (loggedIn && to.path === '/login') {
    // 已登录但访问登录页，重定向到首页
    next('/dashboard');
  } else {
    // 正常导航
    next();
  }
});

export default router



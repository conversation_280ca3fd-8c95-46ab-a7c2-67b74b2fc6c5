package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zhentao.dto.system.system.Page;
import lombok.Data;

import java.util.Date;

/**
 * @TableName old_stu
 */
@TableName(value = "old_stu")
@Data
public class OldStu extends Page {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer oldId;

    /**
     * 学号
     */
    private String oldstuNo;

    /**
     * 学生姓名
     */
    private String stuName;

    /**
     * 性别
     * 1：男
     * 0：女
     * 9：未知
     */
    private Integer gender;

    /**
     * 密码
     */
    private String password;

    /**
     * 角色
     */
    private Integer role;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮件
     */
    private String email;

    /**
     * 介绍
     */
    private String remark;

    /**
     * 班级
     */
    private String className;

    /**
     * 积分
     */
    private Integer points;

    /**
     * 状态
     * 0:正常
     * 1:休学
     * 2:退学
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy年MM月dd日 HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 删除标志
     * 0：删除
     * 1：存在
     */
    private Integer delFlag;


}
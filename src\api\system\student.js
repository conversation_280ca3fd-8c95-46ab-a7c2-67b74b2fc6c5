import request from '@/request';

/**
 * 分页获取学生列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getStudentList(params) {
  const token = localStorage.getItem("Authorization");
  if (!token) {
    return Promise.reject(new Error('未登录或登录已过期'));
  }
  
  // 清理参数
  const cleanParams = { ...params };
  
  // 确保专业名称参数干净
  if (cleanParams.stageName) {
    cleanParams.stageName = cleanParams.stageName.trim();
  }
  
  // 移除空值参数
  Object.keys(cleanParams).forEach(key => {
    if (cleanParams[key] === null || cleanParams[key] === undefined || cleanParams[key] === '') {
      delete cleanParams[key];
    }
  });
  
  return request({
    url: '/edu-student/findPage',
    method: 'post',
    headers: {
      'Authorization': token,
      'Content-Type': 'application/json'
    },
    data: cleanParams
  }).catch(error => {
    console.error('获取学生列表失败:', error);
    throw error;
  });
}

/**
 * 根据ID查询学生
 * @param {Number} studentId 学生ID
 * @returns {Promise}
 */
export function getStudentById(studentId) {
  return request({
    url: '/edu-student/findById',
    method: 'post',
    data: { studentId }
  });
}

/**
 * 添加学生
 * @param {Object} data 学生信息
 * @returns {Promise}
 */
export function addStudent(data) {
  return request({
    url: '/edu-student/addStu',
    method: 'post',
    data
  });
}

/**
 * 更新学生信息
 * @param {Object} data 学生信息
 * @returns {Promise}
 */
export function updateStudent(data) {
  return request({
    url: '/edu-student/updateStu',
    method: 'post',
    data
  });
}

/**
 * 删除学生
 * @param {Number} id 学生ID
 * @returns {Promise}
 */
export function deleteStudent(id) {
  return request({
    url: '/edu-student/delStu',
    method: 'get',
    params: { id }
  });
}

/**
 * 批量删除学生
 * @param {Array} ids 学生ID数组
 * @returns {Promise}
 */
export function batchDeleteStudents(ids) {
  const token = localStorage.getItem("Authorization");
  if (!token) {
    return Promise.reject(new Error('未登录或登录已过期'));
  }

  return request({
    url: '/edu-student/batchDelete',
    method: 'post',
    headers: {
      'Authorization': token,
      'Content-Type': 'application/json'
    },
    data: ids
  }).catch(error => {
    console.error('批量删除学生失败:', error);
    throw error;
  });
}

/**
 * 获取班级选项
 * @returns {Promise}
 */
export function getClassOptions() {
  const token = localStorage.getItem("Authorization");
  if (!token) {
    return Promise.reject(new Error('未登录或登录已过期'));
  }
  
  return request({
    url: '/edu-student/getClassOptions',
    method: 'get',
    headers: {
      'Authorization': token
    }
  }).catch(error => {
    console.error('获取班级选项失败:', error);
    throw error;
  });
}

/**
 * 获取专业选项
 * @returns {Promise}
 */
export function getStageOptions() {
  const token = localStorage.getItem("Authorization");
  if (!token) {
    return Promise.reject(new Error('未登录或登录已过期'));
  }
  
  return request({
    url: '/edu-stage/getSelectableStages',
    method: 'get',
    headers: {
      'Authorization': token
    }
  }).catch(error => {
    console.error('获取专业选项失败:', error);
    throw error;
  });
}

/**
 * 导出学生数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportStudentData(params) {
  const token = localStorage.getItem("Authorization");
  if (!token) {
    return Promise.reject(new Error('未登录或登录已过期'));
  }
  
  // 清理参数
  const cleanParams = { ...params };
  
  // 确保专业名称参数干净
  if (cleanParams.stageName) {
    cleanParams.stageName = cleanParams.stageName.trim();
  }
  
  // 移除空值参数
  Object.keys(cleanParams).forEach(key => {
    if (cleanParams[key] === null || cleanParams[key] === undefined || cleanParams[key] === '') {
      delete cleanParams[key];
    }
  });
  
  return request({
    url: '/api/edu-student/export',
    method: 'post',
    headers: {
      'Authorization': token,
      'Content-Type': 'application/json'
    },
    data: cleanParams,
    responseType: 'blob'
  }).catch(error => {
    console.error('导出学生数据失败:', error);
    throw error;
  });
}

export function saveArchive(){
  return request({
    url: '/edu-student/archive',
    method: 'post',
    timeout: 120000, // 设置2分钟超时，因为归档操作可能需要较长时间
  });
}

export function clearUserAll(){
  return request({
    url: '/edu-student/clear',
    method: 'post',
  });
}

//------------------------横线一下都是cmy写的代码---------------------------------
/**
 * 查询积分前十名的学生
 * @returns {*}
 */
export function queryTopTenStudents() {
  return request({
    url: '/edu-student/queryTopTenStudents',
    method: 'post'
  });
}

/**
 * 查询积分后十名的数据
 */
export function queryBottomTenStudents() {
  return request({
    url: '/edu-student/queryBottomTenStudents',
    method: 'post'
  });
}

/**
 * 查询本班级积分前十名的学生
 * @returns {*}
 */
export function queryTopTenStudentsByClass(classId) {
  return request({
    url: '/edu-student/queryTopTenStudentsByClass?classId='+classId,
    method: 'post'
  });
}

/**
 * 查询本班级积分后十名的数据
 */
export function queryBottomTenStudentsByClass(classId) {
  return request({
    url: '/edu-student/queryBottomTenStudentsByClass?classId='+classId,
    method: 'post'
  });
}

/**
 * 查询班级排名数据
 * @returns {*}
 */
export function queryClassRanking() {
  return request({
    url: '/edu-student/queryClassRanking',
    method: 'post'
  });
}

package com.zhentao.config;

import com.zhentao.utils.Result;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import jakarta.servlet.http.HttpServletRequest;
import java.sql.SQLException;

/**
 * 全局异常处理器
 * 用于统一处理应用中的异常，确保API响应的一致性
 */
@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * 处理数据库相关异常
     */
    @ExceptionHandler({SQLException.class, DataAccessException.class})
    public ResponseEntity<Result> handleDatabaseException(Exception e, HttpServletRequest request) {
        System.err.println("数据库异常 - 请求路径: " + request.getRequestURI());
        System.err.println("异常信息: " + e.getMessage());
        e.printStackTrace();
        
        // 检查是否是连接问题
        if (e.getMessage() != null && e.getMessage().contains("connection")) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.ERROR("数据库连接异常，请稍后重试"));
        }
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.ERROR("数据库操作失败，请联系管理员"));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Result> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        System.err.println("运行时异常 - 请求路径: " + request.getRequestURI());
        System.err.println("异常信息: " + e.getMessage());
        e.printStackTrace();
        
        // 检查是否是批量导入相关的异常
        if (e.getMessage() != null && e.getMessage().contains("批量")) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.ERROR("批量操作异常: " + e.getMessage()));
        }
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.ERROR("系统内部错误: " + e.getMessage()));
    }

    /**
     * 处理一般异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result> handleGeneralException(Exception e, HttpServletRequest request) {
        System.err.println("一般异常 - 请求路径: " + request.getRequestURI());
        System.err.println("异常类型: " + e.getClass().getSimpleName());
        System.err.println("异常信息: " + e.getMessage());
        e.printStackTrace();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.ERROR("操作失败: " + e.getMessage()));
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<Result> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        System.err.println("空指针异常 - 请求路径: " + request.getRequestURI());
        System.err.println("异常信息: " + e.getMessage());
        e.printStackTrace();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.ERROR("系统内部错误，请稍后重试"));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Result> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        System.err.println("参数异常 - 请求路径: " + request.getRequestURI());
        System.err.println("异常信息: " + e.getMessage());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(Result.ERROR("参数错误: " + e.getMessage()));
    }
}

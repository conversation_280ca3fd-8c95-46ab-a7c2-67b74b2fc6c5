package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.SysUser;
import com.zhentao.pojo.SysUserRole;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStageService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.SysUserService;
import com.zhentao.mapper.SysUserRoleMapper;
import com.zhentao.utils.JwtService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户登录注册控制器
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private EduStudentService eduStudentService;

    @Autowired
    private EduClassService eduClassService;

    @Autowired
    private EduStageService eduStageService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private BCryptPasswordEncoder bCryptPasswordEncoder;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result login(@RequestParam String username, @RequestParam String password) {
        try {
            // 查询用户
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username);
            SysUser user = sysUserService.getOne(queryWrapper);

            if (user == null) {
                return Result.ERROR("用户名不存在");
            }

            // 检查用户状态
            if (user.getStatus() == 1) {
                return Result.ERROR("该用户已被禁用，请联系管理员");
            }

            // 验证密码
            if (!bCryptPasswordEncoder.matches(password, user.getPassword())) {
                return Result.ERROR("密码错误");
            }

            // 获取用户角色
            QueryWrapper<SysUserRole> roleQuery = new QueryWrapper<>();
            roleQuery.eq("user_id", user.getUserId());
            List<SysUserRole> userRoles = sysUserRoleMapper.selectList(roleQuery);
            List<String> roleIds = new ArrayList<>();
            for (SysUserRole role : userRoles) {
                roleIds.add(role.getRoleId().toString());
            }

            // 查找对应的学生信息
            EduStudent student = null;
            if (user.getPhone() != null && user.getRealName() != null) {
                QueryWrapper<EduStudent> studentQuery = new QueryWrapper<>();
                studentQuery.eq("phone", user.getPhone())
                        .eq("real_name", user.getRealName());
                student = eduStudentService.getOne(studentQuery);
                
                if (student != null) {
                    // 设置班级和阶段信息
                    if (student.getClassId() != null) {
                        student.setClassName(eduClassService.getById(student.getClassId()).getClassName());
                        if (eduClassService.getById(student.getClassId()).getStageId() != null) {
                            student.setStageName(eduStageService.getById(eduClassService.getById(student.getClassId()).getStageId()).getStageName());
                        }
                    }
                }
            }

            // 生成JWT token
            Map<String, Object> claims = new HashMap<>();
            claims.put("username", user.getUsername());
            claims.put("userId", user.getUserId());
            claims.put("roleIds", roleIds);
            claims.put("student", student);
            String token = JwtService.createToken(claims);

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("username", user.getUsername());
            data.put("avatar", user.getAvatar());
            data.put("roleIds", roleIds);
            data.put("student", student);

            return Result.OK(data);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result register(@RequestBody Map<String, String> registerData) {
        try {
            String name = registerData.get("name");
            String phone = registerData.get("phone");
            String username = registerData.get("username");
            String password = registerData.get("password");
            String captcha = registerData.get("captcha");

            // 验证必填字段
            if (name == null || name.trim().isEmpty()) {
                return Result.ERROR("姓名不能为空");
            }
            if (phone == null || phone.trim().isEmpty()) {
                return Result.ERROR("手机号不能为空");
            }
            if (username == null || username.trim().isEmpty()) {
                return Result.ERROR("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.ERROR("密码不能为空");
            }

            // 检查用户名是否已存在
            QueryWrapper<SysUser> usernameQuery = new QueryWrapper<>();
            usernameQuery.eq("username", username);
            if (sysUserService.count(usernameQuery) > 0) {
                return Result.ERROR("用户名已存在");
            }

            // 检查手机号是否已存在
            QueryWrapper<SysUser> phoneQuery = new QueryWrapper<>();
            phoneQuery.eq("phone", phone);
            if (sysUserService.count(phoneQuery) > 0) {
                return Result.ERROR("手机号已被注册");
            }

            // 检查是否为学生
            QueryWrapper<EduStudent> studentQuery = new QueryWrapper<>();
            studentQuery.eq("phone", phone);
            EduStudent student = eduStudentService.getOne(studentQuery);

            // 创建新用户
            SysUser newUser = new SysUser();
            newUser.setUsername(username);
            newUser.setRealName(name);
            newUser.setPhone(phone);
            newUser.setPassword(bCryptPasswordEncoder.encode(password));
            newUser.setStatus(0); // 默认启用
            newUser.setDelFlag(0);

            // 如果是学生，设置学生角色
            if (student != null) {
                newUser.setUserType(6); // 学生角色
            } else {
                newUser.setUserType(1); // 默认讲师角色
            }

            // 保存用户
            boolean saved = sysUserService.save(newUser);
            if (!saved) {
                return Result.ERROR("注册失败，请稍后重试");
            }

            return Result.OK("注册成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("注册失败：" + e.getMessage());
        }
    }

    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    public Result logout() {
        return Result.OK("退出成功");
    }
} 
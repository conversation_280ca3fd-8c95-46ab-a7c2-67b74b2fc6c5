package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.SysUser;
import com.zhentao.pojo.SysUserRole;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStageService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.SysUserService;
import com.zhentao.mapper.SysUserRoleMapper;
import com.zhentao.utils.JwtService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户登录注册控制器
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private EduStudentService eduStudentService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private BCryptPasswordEncoder bCryptPasswordEncoder;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result register(@RequestBody Map<String, String> registerData) {
        try {

            System.out.println(registerData);

            String name = registerData.get("name");
            String phone = registerData.get("phone");
            String username = registerData.get("username");
            String password = registerData.get("password");
            String captcha = registerData.get("captcha");

            // 验证必填字段
            if (name == null || name.trim().isEmpty()) {
                return Result.ERROR("姓名不能为空");
            }
            if (phone == null || phone.trim().isEmpty()) {
                return Result.ERROR("手机号不能为空");
            }
            if (username == null || username.trim().isEmpty()) {
                return Result.ERROR("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.ERROR("密码不能为空");
            }

            // 检查用户名是否已存在
            QueryWrapper<SysUser> usernameQuery = new QueryWrapper<>();
            usernameQuery.eq("username", username);
            if (sysUserService.count(usernameQuery) > 0) {
                return Result.ERROR("用户名已存在");
            }

            // 检查手机号是否已存在
            QueryWrapper<SysUser> phoneQuery = new QueryWrapper<>();
            phoneQuery.eq("phone", phone);
            if (sysUserService.count(phoneQuery) > 0) {
                return Result.ERROR("手机号已被注册");
            }

            // 检查是否为本院学生
            QueryWrapper<EduStudent> studentQuery = new QueryWrapper<>();
            studentQuery.eq("phone", phone);
            studentQuery.eq("real_name", name);
            EduStudent student = eduStudentService.getOne(studentQuery);

            boolean saved = false;
            // 如果是学生，设置学生角色
            if (student != null) {
                // 创建新用户
                SysUser newUser = new SysUser();
                newUser.setUsername(username);
                newUser.setRealName(name);
                newUser.setPhone(phone);
                newUser.setPassword(bCryptPasswordEncoder.encode(password));
                newUser.setStatus(0); // 默认启用
                newUser.setDelFlag(0);
                newUser.setUserType(7); // 学生角色
                saved = sysUserService.save(newUser);// 保存用户
                // 设置用户角色
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(newUser.getUserId());
                sysUserRole.setRoleId(7);
                sysUserRoleMapper.insert(sysUserRole);
            }
            if (!saved) {
                return Result.ERROR("注册失败，请稍后重试");
            }
            return Result.OK("注册成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("注册失败：" + e.getMessage());
        }
    }

    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    public Result logout() {
        return Result.OK("退出成功");
    }
} 
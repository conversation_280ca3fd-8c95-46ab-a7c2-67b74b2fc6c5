package com.zhentao.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接配置和管理类
 * 用于处理批量操作后的连接状态问题
 */
@Component
public class DatabaseConnectionConfig {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DataSource dataSource;

    /**
     * 重置数据库连接状态
     * 在批量操作后调用，确保连接处于正常状态
     */
    public void resetConnectionState() {
        try {
            // 方法1：执行简单查询来重置连接状态
            jdbcTemplate.execute("SELECT 1");
            
            // 方法2：如果使用HikariCP，可以验证连接
            try (Connection connection = dataSource.getConnection()) {
                if (connection.isValid(5)) {
                    System.out.println("数据库连接状态正常");
                } else {
                    System.out.println("数据库连接状态异常，已重置");
                }
            }
            
            System.out.println("数据库连接状态重置完成");
        } catch (Exception e) {
            System.err.println("重置数据库连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查连接池状态
     */
    public void checkConnectionPoolStatus() {
        try {
            // 这里可以添加连接池状态检查逻辑
            System.out.println("连接池状态检查完成");
        } catch (Exception e) {
            System.err.println("连接池状态检查失败: " + e.getMessage());
        }
    }

    /**
     * 强制刷新所有待处理的批量操作
     */
    public void flushBatchOperations() {
        try {
            // 执行一个简单的查询来确保所有批量操作都已完成
            jdbcTemplate.queryForObject("SELECT COUNT(*) FROM edu_student LIMIT 1", Integer.class);
            System.out.println("批量操作刷新完成");
        } catch (Exception e) {
            System.err.println("刷新批量操作失败: " + e.getMessage());
        }
    }
}

<template>
  <div class="about-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>关于我们</h1>
      <p class="subtitle">了解我们的团队、系统功能和联系方式</p>
    </div>

    <div class="content-wrapper">
      <!-- 系统介绍部分 -->
      <section class="intro-section">
        <div class="intro-card card">
          <div class="card-header">
            <el-icon class="header-icon"><InfoFilled /></el-icon>
            <h2>系统简介</h2>
          </div>
          <div class="intro-content">
            <div class="system-logo">
              <el-icon class="logo-icon"><School /></el-icon>
              <h3>学生积分管理系统</h3>
            </div>
            <div class="system-description">
              <p>
                学生积分管理系统是一个专为高等院校设计的综合性学生管理平台，
                旨在通过科学的积分制度，全面记录和评估学生的学习表现、品德行为、
                社会实践等各个方面的表现。
              </p>
              <p>
                系统采用现代化的Web技术架构，提供直观友好的用户界面，
                支持学生自主查询、申请管理、数据统计等功能，
                帮助学校建立更加公平、透明、高效的学生评价体系。
              </p>
            </div>
          </div>
        </div>

        <div class="features-card card">
          <div class="card-header">
            <el-icon class="header-icon"><Star /></el-icon>
            <h2>核心功能</h2>
          </div>
          <div class="features-grid">
            <div class="feature-item" v-for="feature in features" :key="feature.title">
              <div class="feature-icon">
                <el-icon><component :is="feature.icon" /></el-icon>
              </div>
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 技术架构与团队信息 -->
      <section class="tech-team-section">
        <div class="tech-card card">
          <div class="card-header">
            <el-icon class="header-icon"><Monitor /></el-icon>
            <h2>技术架构</h2>
          </div>
          <div class="tech-content">
            <div class="tech-stack">
              <div 
                class="tech-category-card" 
                v-for="(category, index) in techCategories" 
                :key="index"
              >
                <div class="tech-category-icon">
                  <el-icon>
                    <component :is="category.icon" />
                  </el-icon>
                </div>
                <h3 class="tech-category-title">{{ category.title }}</h3>
                <div class="tech-tags">
                  <el-tag 
                    v-for="tag in category.tags" 
                    :key="tag"
                    size="small"
                    :type="getTechTagType(category.type)"
                    class="skill-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="team-card card">
          <div class="card-header">
            <el-icon class="header-icon"><UserFilled /></el-icon>
            <h2>开发团队</h2>
          </div>
          <div class="team-content">
            <div class="team-intro">
              <p>我们是一支充满活力的全栈开发团队，致力于为教育行业提供优质的信息化解决方案。</p>
            </div>
            <div class="team-members">
              <div 
                v-for="member in teamMembers" 
                :key="member.id"
                class="member-card"
              >
                <div class="member-avatar">
                  <el-avatar :size="80" :src="member.avatar">{{ member.name.charAt(0) }}</el-avatar>
                </div>
                <div class="member-info">
                  <h3>{{ member.name }}</h3>
                  <p class="member-role">{{ member.role }}</p>
                  <p class="member-desc">{{ member.description }}</p>
                  <div class="member-skills">
                    <el-tag 
                      v-for="skill in member.skills" 
                      :key="skill"
                      size="small"
                      type="info"
                      class="skill-tag"
                    >
                      {{ skill }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 联系方式与反馈 -->
      <section class="contact-section">
        <div class="contact-card card">
          <div class="card-header">
            <el-icon class="header-icon"><Phone /></el-icon>
            <h2>联系我们</h2>
          </div>
          <div class="contact-content">
            <div class="contact-grid">
              <div class="contact-item" v-for="contact in contactMethods" :key="contact.title">
                <div class="contact-icon-wrapper">
                  <el-icon class="contact-icon"><component :is="contact.icon" /></el-icon>
                </div>
                <div class="contact-info">
                  <h3>{{ contact.title }}</h3>
                  <p v-for="detail in contact.details" :key="detail">{{ detail }}</p>
                </div>
              </div>
            </div>
            
            <div class="feedback-section">
              <h3>意见反馈</h3>
              <el-form :model="feedbackForm" label-width="80px">
                <el-form-item label="联系方式">
                  <el-input v-model="feedbackForm.contact" placeholder="请输入邮箱或电话" />
                </el-form-item>
                <el-form-item label="反馈内容">
                  <el-input 
                    v-model="feedbackForm.content" 
                    type="textarea" 
                    :rows="4"
                    placeholder="请输入您的意见或建议"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item class="form-actions">
                  <el-button type="primary" @click="submitFeedback" :loading="submittingFeedback">
                    提交反馈
                  </el-button>
                  <el-button @click="resetFeedback">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </section>

      <!-- 版本信息 -->
      <section class="version-section">
        <div class="version-card card">
          <div class="card-header">
            <el-icon class="header-icon"><Document /></el-icon>
            <h2>版本信息</h2>
          </div>
          <div class="version-content">
            <div class="version-main">
              <div class="version-info">
                <h3>当前版本</h3>
                <p class="version-number">v2.1.0</p>
                <p class="version-date">发布日期：2024-12-20</p>
              </div>
              <div class="version-features">
                <h3>版本特性</h3>
                <ul>
                  <li v-for="feature in versionFeatures" :key="feature" class="feature-list-item">
                    <span class="feature-icon">{{ feature.startsWith('✨') ? '✨' : feature.startsWith('🎨') ? '🎨' : feature.startsWith('🐛') ? '🐛' : feature.startsWith('⚡') ? '⚡' : feature.startsWith('🔒') ? '🔒' : '🔹' }}</span>
                    {{ feature.substring(2) }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  InfoFilled, School, Monitor, UserFilled, Phone,Document, Star
} from '@element-plus/icons-vue'

// 核心功能列表
const features = ref([
  {
    icon: 'Check',
    title: '积分记录与查询',
    description: '完整记录学生各项积分，支持实时查询和历史追溯'
  },
  {
    icon: 'DocumentCopy',
    title: '在线申请管理',
    description: '学生可在线提交积分申请，审批流程清晰透明'
  },
  {
    icon: 'User',
    title: '排行榜与统计',
    description: '实时展示各类积分排行榜，支持多维度数据统计'
  },
  {
    icon: 'Star',
    title: '荣誉展示平台',
    description: '集中展示学生获得的荣誉和成就，激励学生积极进取'
  }
])

// 技术架构分类
const techCategories = ref([
  {
    type: 'front',
    title: '前端技术',
    icon: 'Front',
    tags: ['Vue 3', 'Element Plus', 'JavaScript ES6+', 'CSS3']
  },
  {
    type: 'back',
    title: '后端技术',
    icon: 'Backend',
    tags: ['Spring Boot', 'MyBatis Plus', 'MySQL', 'Redis']
  },
  {
    type: 'tool',
    title: '开发工具',
    icon: 'Tools',
    tags: ['IntelliJ IDEA', 'Visual Studio Code', 'Git', 'Maven']
  }
])

// 获取技术标签类型
const getTechTagType = (type) => {
  switch (type) {
    case 'front': return 'primary';
    case 'back': return 'success';
    case 'tool': return 'warning';
    default: return 'info';
  }
};

// 团队成员数据（更新为新的团队成员）
const teamMembers = ref([
  {
    id: 1,
    name: '吴瑞琛',
    role: '全栈开发工程师',
    description: '负责项目整体架构设计和核心功能开发',
    avatar: '',
    skills: ['Spring Boot', 'Vue.js', 'MySQL', 'Redis']
  },
  {
    id: 2,
    name: '李佳隆',
    role: '全栈开发工程师',
    description: '负责前后端功能模块开发和系统集成',
    avatar: '',
    skills: ['Java', 'JavaScript', 'Element Plus', 'MyBatis']
  },
  {
    id: 3,
    name: '刘俊哲',
    role: '全栈开发工程师',
    description: '负责数据库设计和后端服务开发',
    avatar: '',
    skills: ['Spring Boot', 'MySQL', 'Redis', 'RESTful API']
  },
  {
    id: 4,
    name: '陈明毅',
    role: '全栈开发工程师',
    description: '负责前端界面开发和用户体验优化',
    avatar: '',
    skills: ['Vue 3', 'CSS3', 'Element Plus', '响应式设计']
  }
])

// 联系方式
const contactMethods = ref([
  {
    icon: 'Message',
    title: '邮箱联系',
    details: ['<EMAIL>', '<EMAIL>']
  },
  {
    icon: 'Phone',
    title: '电话咨询',
    details: ['技术支持：************', '商务合作：************']
  },
  {
    icon: 'Location',
    title: '办公地址',
    details: ['北京市海淀区中关村软件园', '科技大厦A座15层']
  },
  {
    icon: 'Clock',
    title: '服务时间',
    details: ['周一至周五：9:00-18:00', '周六至周日：10:00-16:00']
  }
])

// 版本特性
const versionFeatures = ref([
  '✨ 新增定时归档功能，支持自动数据归档',
  '🎨 优化用户界面，提升用户体验',
  '🐛 修复已知问题，提高系统稳定性',
  '⚡ 性能优化，提升系统响应速度',
  '🔒 增强安全性，保护用户数据安全'
])

// 反馈表单
const feedbackForm = reactive({
  contact: '',
  content: ''
})

// 提交状态
const submittingFeedback = ref(false)

// 方法
const submitFeedback = () => {
  if (!feedbackForm.contact || !feedbackForm.content) {
    ElMessage.warning('请填写完整的反馈信息')
    return
  }
  
  submittingFeedback.value = true
  
  // 模拟提交
  setTimeout(() => {
    ElMessage.success('感谢您的反馈，我们会认真处理您的意见！')
    resetFeedback()
    submittingFeedback.value = false
  }, 1000)
}

const resetFeedback = () => {
  feedbackForm.contact = ''
  feedbackForm.content = ''
}
</script>

<style scoped>
.about-page {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  background: linear-gradient(135deg, #409eff, #5470c6);
  color: white;
  border-radius: 8px;
}

.page-header h1 {
  margin: 0 0 12px 0;
  font-size: 2.5rem;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

.content-wrapper {
  display: grid;
  gap: 24px;
}

.card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

/* 卡片通用样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.header-icon {
  font-size: 20px;
  color: #409eff;
}

/* 系统介绍卡片 */
.intro-section {
  display: grid;
  gap: 24px;
}

.system-logo {
  text-align: center;
  margin-bottom: 24px;
}

.logo-icon {
  font-size: 64px;
  color: #409eff;
  margin-bottom: 16px;
}

.system-logo h3 {
  margin: 0;
  color: #303133;
  font-size: 24px;
}

.system-description p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 16px;
}

/* 核心功能卡片 */
.features-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.feature-icon {
  color: #409eff;
  margin-bottom: 12px;
}

.feature-icon .el-icon {
  font-size: 24px;
}

/* 技术架构与团队信息 */
.tech-team-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.tech-stack {
  display: grid;
  gap: 24px;
}

/* 技术分类卡片 */
.tech-category-card {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.tech-category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.tech-category-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 12px;
}

.tech-category-title {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-tag {
  margin-right: 6px;
  margin-bottom: 6px;
}

/* 团队成员卡片 */
.member-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.member-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.member-avatar {
  flex-shrink: 0;
}

.member-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
}

.member-role {
  margin: 0 0 8px 0;
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
}

.member-desc {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 联系我们卡片 */
.contact-section {
  margin-top: 24px;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.contact-icon-wrapper {
  flex-shrink: 0;
  margin-top: 4px;
}

.contact-icon {
  font-size: 24px;
  color: #409eff;
}

.contact-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.contact-info p {
  margin: 0 0 4px 0;
  color: #606266;
  font-size: 14px;
}

.feedback-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.feedback-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.form-actions {
  margin-bottom: 0;
}

/* 版本信息卡片 */
.version-section {
  margin-top: 24px;
}

.version-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
}

.version-info {
  text-align: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.version-info h3 {
  margin: 0 0 12px 0;
  color: #303133;
}

.version-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin: 0 0 8px 0;
}

.version-date {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.version-features h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.version-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list-item {
  margin-bottom: 12px;
  color: #606266;
  line-height: 1.5;
}

.feature-icon {
  margin-right: 8px;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .tech-team-section {
    grid-template-columns: 1fr;
  }
  
  .version-main {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    margin-bottom: 20px;
  }
  
  .content-wrapper {
    gap: 16px;
  }
  
  .tech-team-section {
    gap: 16px;
  }
  
  .features-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .version-main {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .member-card {
    flex-direction: column;
    text-align: center;
  }
  
  .member-avatar {
    margin-bottom: 12px;
  }
}
</style>
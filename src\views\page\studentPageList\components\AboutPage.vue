<template>
  <div class="about-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>关于我们</h2>
      <p>了解我们的团队、系统功能和联系方式</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：系统介绍 -->
      <el-col :span="12">
        <!-- 系统简介 -->
        <el-card class="intro-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>系统简介</span>
            </div>
          </template>
          <div class="intro-content">
            <div class="system-logo">
              <el-icon class="logo-icon"><School /></el-icon>
              <h3>学生积分管理系统</h3>
            </div>
            <div class="system-description">
              <p>
                学生积分管理系统是一个专为高等院校设计的综合性学生管理平台，
                旨在通过科学的积分制度，全面记录和评估学生的学习表现、品德行为、
                社会实践等各个方面的表现。
              </p>
              <p>
                系统采用现代化的Web技术架构，提供直观友好的用户界面，
                支持学生自主查询、申请管理、数据统计等功能，
                帮助学校建立更加公平、透明、高效的学生评价体系。
              </p>
            </div>
            <div class="system-features">
              <h4>核心功能</h4>
              <ul>
                <li><el-icon><Check /></el-icon> 积分记录与查询</li>
                <li><el-icon><Check /></el-icon> 在线申请管理</li>
                <li><el-icon><Check /></el-icon> 排行榜与统计</li>
                <li><el-icon><Check /></el-icon> 荣誉展示平台</li>
                <li><el-icon><Check /></el-icon> 通知公告发布</li>
                <li><el-icon><Check /></el-icon> 个人信息管理</li>
              </ul>
            </div>
          </div>
        </el-card>

        <!-- 技术架构 -->
        <el-card class="tech-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>技术架构</span>
            </div>
          </template>
          <div class="tech-content">
            <div class="tech-stack">
              <div class="tech-category">
                <h4>前端技术</h4>
                <div class="tech-tags">
                  <el-tag type="primary">Vue 3</el-tag>
                  <el-tag type="success">Element Plus</el-tag>
                  <el-tag type="info">JavaScript ES6+</el-tag>
                  <el-tag type="warning">CSS3</el-tag>
                </div>
              </div>
              <div class="tech-category">
                <h4>后端技术</h4>
                <div class="tech-tags">
                  <el-tag type="primary">Spring Boot</el-tag>
                  <el-tag type="success">MyBatis Plus</el-tag>
                  <el-tag type="info">MySQL</el-tag>
                  <el-tag type="warning">Redis</el-tag>
                </div>
              </div>
              <div class="tech-category">
                <h4>开发工具</h4>
                <div class="tech-tags">
                  <el-tag>IntelliJ IDEA</el-tag>
                  <el-tag>Visual Studio Code</el-tag>
                  <el-tag>Git</el-tag>
                  <el-tag>Maven</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：团队信息 -->
      <el-col :span="12">
        <!-- 开发团队 -->
        <el-card class="team-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><UserFilled /></el-icon>
              <span>开发团队</span>
            </div>
          </template>
          <div class="team-content">
            <div class="team-intro">
              <p>我们是一支充满活力的开发团队，致力于为教育行业提供优质的信息化解决方案。</p>
            </div>
            <div class="team-members">
              <div 
                v-for="member in teamMembers" 
                :key="member.id"
                class="member-card"
              >
                <el-avatar :size="60" :src="member.avatar">{{ member.name.charAt(0) }}</el-avatar>
                <div class="member-info">
                  <h4>{{ member.name }}</h4>
                  <p class="member-role">{{ member.role }}</p>
                  <p class="member-desc">{{ member.description }}</p>
                  <div class="member-skills">
                    <el-tag 
                      v-for="skill in member.skills" 
                      :key="skill"
                      size="small"
                      type="info"
                    >
                      {{ skill }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 联系我们 -->
        <el-card class="contact-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Phone /></el-icon>
              <span>联系我们</span>
            </div>
          </template>
          <div class="contact-content">
            <div class="contact-methods">
              <div class="contact-item">
                <el-icon class="contact-icon"><Message /></el-icon>
                <div class="contact-info">
                  <h4>邮箱联系</h4>
                  <p><EMAIL></p>
                  <p><EMAIL></p>
                </div>
              </div>
              <div class="contact-item">
                <el-icon class="contact-icon"><Phone /></el-icon>
                <div class="contact-info">
                  <h4>电话咨询</h4>
                  <p>技术支持：************</p>
                  <p>商务合作：************</p>
                </div>
              </div>
              <div class="contact-item">
                <el-icon class="contact-icon"><Location /></el-icon>
                <div class="contact-info">
                  <h4>办公地址</h4>
                  <p>北京市海淀区中关村软件园</p>
                  <p>科技大厦A座15层</p>
                </div>
              </div>
              <div class="contact-item">
                <el-icon class="contact-icon"><Clock /></el-icon>
                <div class="contact-info">
                  <h4>服务时间</h4>
                  <p>周一至周五：9:00-18:00</p>
                  <p>周六至周日：10:00-16:00</p>
                </div>
              </div>
            </div>
            <div class="feedback-section">
              <h4>意见反馈</h4>
              <el-form :model="feedbackForm" label-width="80px">
                <el-form-item label="联系方式">
                  <el-input v-model="feedbackForm.contact" placeholder="请输入邮箱或电话" />
                </el-form-item>
                <el-form-item label="反馈内容">
                  <el-input 
                    v-model="feedbackForm.content" 
                    type="textarea" 
                    :rows="4"
                    placeholder="请输入您的意见或建议"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitFeedback" :loading="submittingFeedback">
                    提交反馈
                  </el-button>
                  <el-button @click="resetFeedback">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 版本信息 -->
    <el-card class="version-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>版本信息</span>
        </div>
      </template>
      <div class="version-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="version-info">
              <h4>当前版本</h4>
              <p class="version-number">v2.1.0</p>
              <p class="version-date">发布日期：2024-12-20</p>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="version-features">
              <h4>版本特性</h4>
              <ul>
                <li>✨ 新增定时归档功能，支持自动数据归档</li>
                <li>🎨 优化用户界面，提升用户体验</li>
                <li>🐛 修复已知问题，提高系统稳定性</li>
                <li>⚡ 性能优化，提升系统响应速度</li>
                <li>🔒 增强安全性，保护用户数据安全</li>
              </ul>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  InfoFilled, School, Check, Monitor, UserFilled, 
  Phone, Message, Location, Clock, Document 
} from '@element-plus/icons-vue'

// 团队成员数据
const teamMembers = ref([
  {
    id: 1,
    name: '张三',
    role: '项目经理 & 后端开发',
    description: '负责项目整体规划和后端架构设计',
    avatar: '',
    skills: ['Spring Boot', 'MySQL', '项目管理']
  },
  {
    id: 2,
    name: '李四',
    role: '前端开发工程师',
    description: '负责前端界面设计和用户体验优化',
    avatar: '',
    skills: ['Vue.js', 'Element Plus', 'CSS3']
  },
  {
    id: 3,
    name: '王五',
    role: '全栈开发工程师',
    description: '负责系统集成和功能模块开发',
    avatar: '',
    skills: ['Java', 'JavaScript', 'Redis']
  },
  {
    id: 4,
    name: '赵六',
    role: '测试工程师',
    description: '负责系统测试和质量保证',
    avatar: '',
    skills: ['测试用例', '自动化测试', '性能测试']
  }
])

// 反馈表单
const feedbackForm = reactive({
  contact: '',
  content: ''
})

// 提交状态
const submittingFeedback = ref(false)

// 方法
const submitFeedback = () => {
  if (!feedbackForm.contact || !feedbackForm.content) {
    ElMessage.warning('请填写完整的反馈信息')
    return
  }
  
  submittingFeedback.value = true
  
  // 模拟提交
  setTimeout(() => {
    ElMessage.success('感谢您的反馈，我们会认真处理您的意见！')
    resetFeedback()
    submittingFeedback.value = false
  }, 1000)
}

const resetFeedback = () => {
  feedbackForm.contact = ''
  feedbackForm.content = ''
}
</script>

<style scoped>
.about-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 系统简介卡片 */
.intro-card {
  margin-bottom: 20px;
}

.system-logo {
  text-align: center;
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 10px;
}

.system-logo h3 {
  margin: 0;
  color: #303133;
  font-size: 24px;
}

.system-description p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 16px;
}

.system-features h4 {
  margin: 20px 0 12px 0;
  color: #303133;
}

.system-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.system-features li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #606266;
}

.system-features li .el-icon {
  color: #67c23a;
}

/* 技术架构卡片 */
.tech-card {
  margin-bottom: 20px;
}

.tech-category {
  margin-bottom: 20px;
}

.tech-category h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 团队卡片 */
.team-card {
  margin-bottom: 20px;
}

.team-intro p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 20px;
}

.member-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.member-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
}

.member-role {
  margin: 0 0 8px 0;
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
}

.member-desc {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 联系我们卡片 */
.contact-card {
  margin-bottom: 20px;
}

.contact-methods {
  margin-bottom: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
}

.contact-icon {
  font-size: 24px;
  color: #409eff;
  margin-top: 4px;
}

.contact-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.contact-info p {
  margin: 0 0 4px 0;
  color: #606266;
  font-size: 14px;
}

.feedback-section {
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.feedback-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

/* 版本信息卡片 */
.version-card {
  margin-bottom: 20px;
}

.version-info {
  text-align: center;
}

.version-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.version-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin: 0 0 8px 0;
}

.version-date {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.version-features h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.version-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.version-features li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-card {
    flex-direction: column;
    text-align: center;
  }
  
  .contact-item {
    flex-direction: column;
    text-align: center;
  }
  
  .tech-tags {
    justify-content: center;
  }
  
  .member-skills {
    justify-content: center;
  }
}
</style>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量删除功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #c82333;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.success:hover {
            background: #218838;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .student-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .student-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .student-item:last-child {
            border-bottom: none;
        }
        .student-info {
            flex: 1;
        }
        .student-checkbox {
            margin-right: 10px;
        }
        .selected-count {
            font-weight: bold;
            color: #dc3545;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗑️ 批量删除学生功能测试</h1>
        <p><strong>功能描述：</strong>测试学生管理系统的批量删除功能</p>
        <p><strong>注意：</strong>此功能会真实删除数据，请谨慎操作！</p>
    </div>

    <div class="container">
        <h2>🧪 测试步骤</h2>
        
        <div class="test-section">
            <h3>1. 获取学生列表</h3>
            <p>首先获取当前系统中的学生数据</p>
            <button class="test-button success" onclick="fetchStudentList()">获取学生列表</button>
            <div id="student-list-result" class="result"></div>
            <div id="student-list-container" class="student-list" style="display: none;">
                <!-- 学生列表将在这里显示 -->
            </div>
            <div id="selected-count" class="selected-count" style="display: none;">
                已选择 0 名学生
            </div>
        </div>

        <div class="test-section">
            <h3>2. 选择要删除的学生</h3>
            <p>从上面的列表中选择要删除的学生（建议选择测试数据）</p>
            <button class="test-button" onclick="selectAllTestStudents()">选择所有测试学生</button>
            <button class="test-button" onclick="clearSelection()">清空选择</button>
        </div>

        <div class="test-section">
            <h3>3. 执行批量删除</h3>
            <p>删除选中的学生数据</p>
            <button class="test-button" onclick="batchDeleteStudents()" id="batch-delete-btn" disabled>批量删除选中学生</button>
            <div id="batch-delete-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 验证删除结果</h3>
            <p>重新获取学生列表，验证删除是否成功</p>
            <button class="test-button success" onclick="verifyDeletion()">验证删除结果</button>
            <div id="verify-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 测试结果总览</h2>
        <div id="test-summary">
            <p>请按顺序执行上述测试...</p>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let studentList = [];
        let selectedStudents = [];
        let testResults = {
            fetchList: null,
            batchDelete: null,
            verify: null
        };

        // 获取认证token
        function getAuthToken() {
            return localStorage.getItem('Authorization') || '';
        }

        // 通用API请求函数
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': getAuthToken()
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            try {
                const response = await fetch(API_BASE + url, finalOptions);
                const data = await response.json();
                return {
                    success: response.ok && data.code === 200,
                    data: data,
                    status: response.status,
                    statusText: response.statusText
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    data: null
                };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
        }

        // 获取学生列表
        async function fetchStudentList() {
            showResult('student-list-result', '正在获取学生列表...', 'info');
            
            try {
                const result = await apiRequest('/edu-student/findPage', {
                    method: 'POST',
                    body: JSON.stringify({ pageNum: 1, pageSize: 100 })
                });

                if (result.success && result.data.data) {
                    studentList = result.data.data.records || [];
                    testResults.fetchList = true;
                    
                    showResult('student-list-result', 
                        `✅ 获取成功，共 ${studentList.length} 名学生`, 
                        'success');
                    
                    displayStudentList();
                } else {
                    testResults.fetchList = false;
                    showResult('student-list-result', 
                        `❌ 获取失败: ${result.error || result.data?.message || '未知错误'}`, 
                        'error');
                }
            } catch (error) {
                testResults.fetchList = false;
                showResult('student-list-result', `❌ 获取异常: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }

        // 显示学生列表
        function displayStudentList() {
            const container = document.getElementById('student-list-container');
            const countElement = document.getElementById('selected-count');
            
            if (studentList.length === 0) {
                container.innerHTML = '<p>暂无学生数据</p>';
                container.style.display = 'block';
                return;
            }

            let html = '';
            studentList.forEach(student => {
                const isTestStudent = student.studentNo.includes('TEST') || student.realName.includes('测试');
                html += `
                    <div class="student-item">
                        <div class="student-info">
                            <input type="checkbox" class="student-checkbox" 
                                   value="${student.studentId}" 
                                   onchange="handleStudentSelection(${student.studentId}, this.checked)"
                                   ${isTestStudent ? 'data-test="true"' : ''}>
                            <strong>${student.realName}</strong> (${student.studentNo}) - ${student.className || '未知班级'}
                            ${isTestStudent ? '<span style="color: #dc3545;">[测试数据]</span>' : ''}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            container.style.display = 'block';
            countElement.style.display = 'block';
            updateSelectedCount();
        }

        // 处理学生选择
        function handleStudentSelection(studentId, checked) {
            if (checked) {
                if (!selectedStudents.includes(studentId)) {
                    selectedStudents.push(studentId);
                }
            } else {
                selectedStudents = selectedStudents.filter(id => id !== studentId);
            }
            updateSelectedCount();
        }

        // 更新选中数量显示
        function updateSelectedCount() {
            const countElement = document.getElementById('selected-count');
            const deleteBtn = document.getElementById('batch-delete-btn');
            
            countElement.textContent = `已选择 ${selectedStudents.length} 名学生`;
            deleteBtn.disabled = selectedStudents.length === 0;
        }

        // 选择所有测试学生
        function selectAllTestStudents() {
            const checkboxes = document.querySelectorAll('input[data-test="true"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                handleStudentSelection(parseInt(checkbox.value), true);
            });
        }

        // 清空选择
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectedStudents = [];
            updateSelectedCount();
        }

        // 批量删除学生
        async function batchDeleteStudents() {
            if (selectedStudents.length === 0) {
                showResult('batch-delete-result', '❌ 请先选择要删除的学生', 'warning');
                return;
            }

            const selectedNames = selectedStudents.map(id => {
                const student = studentList.find(s => s.studentId === id);
                return student ? student.realName : '未知';
            }).join('、');

            if (!confirm(`确定要删除以下 ${selectedStudents.length} 名学生吗？\n\n${selectedNames}\n\n此操作不可撤销！`)) {
                return;
            }

            showResult('batch-delete-result', '正在执行批量删除...', 'info');
            
            try {
                const result = await apiRequest('/edu-student/batchDelete', {
                    method: 'POST',
                    body: JSON.stringify(selectedStudents)
                });

                if (result.success) {
                    testResults.batchDelete = true;
                    showResult('batch-delete-result', 
                        `✅ 批量删除成功: ${result.data.message || '删除完成'}`, 
                        'success');
                    
                    // 清空选择并重新获取列表
                    selectedStudents = [];
                    await fetchStudentList();
                } else {
                    testResults.batchDelete = false;
                    showResult('batch-delete-result', 
                        `❌ 批量删除失败: ${result.error || result.data?.message || '未知错误'}`, 
                        'error');
                }
            } catch (error) {
                testResults.batchDelete = false;
                showResult('batch-delete-result', `❌ 批量删除异常: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }

        // 验证删除结果
        async function verifyDeletion() {
            showResult('verify-result', '正在验证删除结果...', 'info');
            
            try {
                const result = await apiRequest('/edu-student/findPage', {
                    method: 'POST',
                    body: JSON.stringify({ pageNum: 1, pageSize: 100 })
                });

                if (result.success && result.data.data) {
                    const currentStudents = result.data.data.records || [];
                    testResults.verify = true;
                    
                    showResult('verify-result', 
                        `✅ 验证完成，当前共有 ${currentStudents.length} 名学生\n删除操作已生效`, 
                        'success');
                } else {
                    testResults.verify = false;
                    showResult('verify-result', 
                        `❌ 验证失败: ${result.error || result.data?.message || '未知错误'}`, 
                        'error');
                }
            } catch (error) {
                testResults.verify = false;
                showResult('verify-result', `❌ 验证异常: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }

        // 更新测试总览
        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            let html = '<h3>测试结果：</h3>';
            
            const testNames = {
                fetchList: '获取学生列表',
                batchDelete: '批量删除',
                verify: '验证删除结果'
            };
            
            Object.entries(testResults).forEach(([key, result]) => {
                const status = result === null ? 'info' : (result ? 'success' : 'error');
                const statusText = result === null ? '未测试' : (result ? '通过' : '失败');
                const statusIcon = result === null ? '⏳' : (result ? '✅' : '❌');
                
                html += `<p>${statusIcon} ${testNames[key]}: ${statusText}</p>`;
            });
            
            summary.innerHTML = html;
        }

        // 初始化
        updateTestSummary();
    </script>
</body>
</html>

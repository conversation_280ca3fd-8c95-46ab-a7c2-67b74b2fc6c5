<template>
  <div class="batch-import-list-container">
    <!-- 上传与模板下载卡片 -->
    <el-card class="upload-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>学院师生信息导入</h3>
          <div class="header-buttons">
            <el-button type="info" size="small" @click="testApiConnection">测试连接</el-button>
            <el-button type="primary" @click="downloadTemplate">下载模板</el-button>
          </div>
        </div>
      </template>
      <div class="upload-area">
        <el-upload
          class="upload-component"
          drag
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls,.et,.ett"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls/et/ett 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <div class="upload-actions" v-if="fileList.length > 0">
          <el-button type="primary" @click="parseFile" :loading="parsing">解析文件</el-button>
          <el-button @click="clearFile">清除文件</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据预览卡片 -->
    <el-card class="preview-card" shadow="hover" v-if="classesData.length > 0">
      <template #header>
        <div class="card-header">
          <h3>数据预览</h3>
          <el-button
            type="primary"
            @click="importData"
            :loading="importing"
            :disabled="validCount === 0"
            >开始导入
          </el-button>
        </div>
      </template>

      <!-- 按班级分组显示数据 -->
      <div class="class-groups-container">
        <!-- 总体统计信息 -->
        <div class="overall-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总班级数" :value="classesData.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总学生数" :value="totalStudentCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="有效数据" :value="validCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="无效数据" :value="totalStudentCount - validCount" />
            </el-col>
          </el-row>
        </div>

        <!-- 班级分组展示 -->
        <div class="class-groups">
          <div v-for="(classData, classIndex) in classesData" :key="classIndex" class="class-group-card">
            <el-card shadow="hover" class="class-card">
              <!-- 班级标题 -->
              <template #header>
                <div class="class-card-header">
                  <div class="class-title">
                    <h3>{{ classData.className || `班级${classIndex + 1}` }}</h3>
                    <el-tag type="primary" size="large">{{ classData.students.length }}人</el-tag>
                  </div>
                  <div class="class-stats">
                    <el-tag 
                      :type="getClassValidCount(classData) === classData.students.length ? 'success' : 'warning'" 
                      size="small"
                    >
                      有效: {{ getClassValidCount(classData) }}/{{ classData.students.length }}
                    </el-tag>
                  </div>
                </div>
              </template>

              <!-- 班级信息 -->
              <div class="class-info">
                <el-descriptions :column="2" size="small" border>
                  <el-descriptions-item label="辅导员">
                    <el-tag type="info" size="small">{{ classData.counselor || '未设置' }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="讲师">
                    <el-tag type="info" size="small">{{ classData.teacher || '未设置' }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="教室号">
                    <el-tag type="info" size="small">{{ classData.classroom || '未设置' }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="所属月度">
                    <el-tag type="info" size="small">{{ classData.stageName || '未设置' }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="课程名称" :span="2">
                    <el-tag type="primary" size="small">{{ classData.courseName || '未设置' }}</el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 学生列表 -->
              <div class="students-section" v-if="classData.students.length > 0">
                <div class="students-header">
                  <h4>学生名单</h4>
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="toggleStudentList(classIndex)"
                    :icon="expandedClasses.includes(classIndex) ? 'ArrowUp' : 'ArrowDown'"
                  >
                    {{ expandedClasses.includes(classIndex) ? '收起' : '展开' }}
                  </el-button>
                </div>
                
                <el-collapse-transition>
                  <div v-show="expandedClasses.includes(classIndex)" class="students-table">
                    <el-table 
                      :data="classData.students" 
                      style="width: 100%" 
                      border 
                      stripe 
                      size="small"
                      :max-height="400"
                    >
                      <el-table-column type="index" width="60" label="序号" align="center" />
                      <el-table-column prop="realName" label="姓名" width="120" align="center">
                        <template #default="scope">
                          <span :class="{ 'invalid-data': !scope.row.isValid }">
                            {{ scope.row.realName }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="studentNo" label="学号" min-width="140" align="center">
                        <template #default="scope">
                          <span :class="{ 'invalid-data': !scope.row.isValid }">
                            {{ scope.row.studentNo }}
                          </span>
                          <el-tooltip v-if="!scope.row.isValid" content="数据格式不正确" placement="top">
                            <el-icon class="warning-icon"><Warning /></el-icon>
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" width="80" align="center">
                        <template #default="scope">
                          <el-tag 
                            :type="scope.row.isValid ? 'success' : 'danger'" 
                            size="small"
                          >
                            {{ scope.row.isValid ? '有效' : '无效' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-collapse-transition>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-students">
                <el-empty description="该班级暂无学生数据" :image-size="80" />
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 导入结果卡片 -->
    <el-card class="result-card" shadow="hover" v-if="importResult.show">
      <template #header>
        <div class="card-header">
          <h3>{{ importResult.title }}</h3>
          <el-button @click="resetImport">重新导入</el-button>
        </div>
      </template>
      
      <el-result
        :icon="importResult.success ? 'success' : 'error'"
        :title="importResult.title"
        :sub-title="importResult.message"
      >
        <template #extra>
          <div class="result-stats">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="总数据">{{ importResult.totalCount }}</el-descriptions-item>
              <el-descriptions-item label="成功">{{ importResult.successCount }}</el-descriptions-item>
              <el-descriptions-item label="失败">{{ importResult.failCount }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
      </el-result>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { UploadFilled, Warning } from '@element-plus/icons-vue';
import * as XLSX from 'xlsx';
import { batchImportStudents, downloadStudentTemplate, validateImportData } from '@/api/system/batchImport';

// 文件上传相关
const fileList = ref([]);
const parsing = ref(false);
const importing = ref(false);
const previewData = ref([]);

// 班级数据结构
const classesData = ref([]);

// 展开的班级列表（用于控制学生列表的展开/收起）
const expandedClasses = ref([]);

// 测试API连接
const testApiConnection = async () => {
  try {
    console.log('测试API连接...');
    const response = await fetch('/api/edu-student/template/download', {
      method: 'GET',
      headers: {
        'Authorization': localStorage.getItem('Authorization') || ''
      }
    });
    console.log('API连接测试结果:', response.status, response.statusText);
    if (response.ok) {
      ElMessage.success('API连接正常');
    } else {
      ElMessage.error(`API连接失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('API连接测试失败:', error);
    ElMessage.error('API连接测试失败: ' + error.message);
  }
};

// 导入结果
const importResult = reactive({
  show: false,
  success: false,
  title: '',
  message: '',
  totalCount: 0,
  successCount: 0,
  failCount: 0
});

// 计算总学生数
const totalStudentCount = computed(() => {
  return classesData.value.reduce((total, classData) => {
    return total + classData.students.length;
  }, 0);
});

// 将所有学生数据扁平化为表格数据
const flattenedStudentData = computed(() => {
  const result = [];
  classesData.value.forEach(classData => {
    classData.students.forEach(student => {
      result.push({
        studentNo: student.studentNo,
        realName: student.realName,
        stageName: classData.stageName,
        courseName: classData.courseName,
        teacher: classData.teacher,
        counselor: classData.counselor,
        className: classData.className,
        classroom: classData.classroom,
        isValid: student.isValid
      });
    });
  });
  return result;
});

// 计算有效数据数量
const validCount = computed(() => {
  let count = 0;
  classesData.value.forEach(cls => {
    count += cls.students.filter(student => student.isValid).length;
  });
  return count;
});

// 获取班级有效学生数量
const getClassValidCount = (classData) => {
  return classData.students.filter(student => student.isValid).length;
};

// 切换学生列表展开/收起状态
const toggleStudentList = (classIndex) => {
  const index = expandedClasses.value.indexOf(classIndex);
  if (index > -1) {
    expandedClasses.value.splice(index, 1);
  } else {
    expandedClasses.value.push(classIndex);
  }
};

// 处理文件变更
const handleFileChange = (file, fileListArr) => {
  fileList.value = [file];
  previewData.value = [];
  classesData.value = [];
  expandedClasses.value = [];
  importResult.show = false;
};

// 清除文件
const clearFile = () => {
  fileList.value = [];
  previewData.value = [];
  classesData.value = [];
  expandedClasses.value = [];
  importResult.show = false;
};

// 下载模板
const downloadTemplate = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在下载模板...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    console.log('开始下载模板...');

    const response = await downloadStudentTemplate();
    console.log('模板下载响应:', response);

    loading.close();

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `学生信息导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('模板下载成功');
  } catch (error) {
    loading.close();

    console.error('下载模板失败详细信息:', error);
    console.error('错误响应:', error.response);
    console.error('错误请求:', error.request);

    let errorMessage = '下载模板失败';
    if (error.response) {
      errorMessage = `服务器错误 (${error.response.status}): ${error.response.data?.message || error.response.statusText}`;
    } else if (error.request) {
      errorMessage = '网络连接失败，请检查网络连接或后端服务是否正常运行';
    } else {
      errorMessage = `请求配置错误: ${error.message}`;
    }

    ElMessage.error(errorMessage);
  }
};

// 解析文件（实现Excel解析并填充classesData）
const parseFile = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传文件');
    return;
  }
  parsing.value = true;
  const file = fileList.value[0].raw;
  const reader = new FileReader();

  console.log('开始解析文件'+file.name);
  // 切割file.name中的7.24日期
  const date = file.name.split('-')[1];
  var match = date.match("(\\d+\\.\\d+)")[0];
  const dataObj = new Date().getFullYear()
  const fullDate = dataObj+"."+match;
  

  reader.onload = (e) => {
    try {
      // 读取Excel数据
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '' });

      if (!jsonData || jsonData.length < 8) {
        parsing.value = false;
        ElMessage.warning('未识别到有效数据，请确保Excel格式正确');
        return;
      }

      // 解析表头信息（前6行）
      const classNames = [];
      const counselors = [];
      const teachers = [];
      const classrooms = [];
      const stageNames = [];
      const courseNames = [];

      // 从第1行开始解析班级信息（每两列一个班级）
      for (let col = 1; col < jsonData[0].length; col += 2) {
        const className = jsonData[0][col] ? jsonData[0][col].toString().trim() : '';
        if (className) {
          classNames.push(className);
          counselors.push(jsonData[1] && jsonData[1][col] ? jsonData[1][col].toString().trim() : '');
          teachers.push(jsonData[2] && jsonData[2][col] ? jsonData[2][col].toString().trim() : '');
          classrooms.push(jsonData[3] && jsonData[3][col] ? jsonData[3][col].toString().trim() : '');
          stageNames.push(jsonData[4] && jsonData[4][col] ? jsonData[4][col].toString().trim() : '');
          courseNames.push(jsonData[5] && jsonData[5][col] ? jsonData[5][col].toString().trim() : '');
        }
      }

      // console.log('解析到的班级信息:', { classNames, counselors, teachers, classrooms, stageNames, courseNames });

      // 创建班级数据结构
      const classes = classNames.map((className, index) => ({
        className: className,
        counselor: counselors[index] || '',
        teacher: teachers[index] || '',
        classroom: classrooms[index] || '',
        stageName: stageNames[index] || '',
        courseName: courseNames[index] || '',
        students: []
      }));

      // 解析学生数据（从第8行开始）
      // console.log('开始解析学生数据，总行数:', jsonData.length);

      for (let row = 7; row < jsonData.length; row++) {
        const rowData = jsonData[row];
        if (!rowData) continue;

        // 根据Excel格式，学生数据按列分布
        // 每两列为一个班级：第一列是姓名，第二列是学号
        for (let col = 1; col < rowData.length; col += 2) {
          const realName = rowData[col] ? rowData[col].toString().trim() : '';
          const studentNo = rowData[col + 1] ? rowData[col + 1].toString().trim() : '';

          // 只要有姓名或学号其中之一就认为是有效数据
          if (realName || studentNo) {
            // 计算班级索引（每两列一个班级）
            const classIndex = Math.floor((col - 1) / 2);

            if (classIndex < classes.length) {
              const studentInfo = {
                studentNo: studentNo || `temp_${Date.now()}_${row}_${col}`,
                realName: realName || '未知姓名',
                isValid: studentNo && /^\d{8,}$/.test(studentNo) && realName && realName.length > 0
              };

              classes[classIndex].students.push(studentInfo);
              // console.log(`添加学生: ${studentInfo.realName} (${studentInfo.studentNo}) 到班级 ${classIndex}`);
            }
          }
        }
      }

      // console.log('解析完成，各班级学生数量:', classes.map(cls => cls.students.length));

      classesData.value = classes;

      // 默认展开所有班级
      expandedClasses.value = classes.map((_, index) => index);

      // 计算总的有效学生数
      let totalStudents = 0;
      let validStudents = 0;
      classes.forEach(cls => {
        totalStudents += cls.students.length;
        validStudents += cls.students.filter(s => s.isValid).length;
      });

      if (totalStudents === 0) {
        ElMessage.warning('没有找到有效的学生数据');
        parsing.value = false;
        return;
      }

      ElMessage.success(`成功解析 ${totalStudents} 条学生数据，其中有效数据 ${validStudents} 条`);
      parsing.value = false;
    } catch (error) {
      console.error('解析文件失败:', error);
      ElMessage.error('解析文件失败：' + error.message);
      parsing.value = false;
    }
  };

  reader.onerror = () => {
    ElMessage.error('文件读取失败');
    parsing.value = false;
  };

  reader.readAsArrayBuffer(file);
};

// 导入数据
const importData = async () => {
  if (validCount.value === 0) {
    ElMessage.warning('没有有效数据可以导入');
    return;
  }

  try {
    // 确认导入
    await ElMessageBox.confirm(
      `确定要导入 ${validCount.value} 条有效数据吗？无效数据将被忽略。`,
      '确认导入',
      {
        confirmButtonText: '确定导入',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    importing.value = true;

    // 准备导入数据
    const importDataPayload = {
      classes: classesData.value.map(classData => ({
        className: classData.className,
        counselor: classData.counselor,
        teacher: classData.teacher,
        classroom: classData.classroom,
        stageName: classData.stageName,
        courseName: classData.courseName,
        students: classData.students.filter(student => student.isValid).map(student => ({
          studentNo: student.studentNo,
          realName: student.realName
        }))
      })).filter(classData => classData.students.length > 0)
    };

    console.log('准备发送的导入数据:', importDataPayload);

    // 显示进度提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在导入数据，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    let response = null;
    try {
      response = await batchImportStudents(importDataPayload);
      console.log('后端响应:', response);
      loading.close();
    } catch (error) {
      loading.close();
      throw error;
    }

    // 处理导入结果
    const responseData = response.data || response;

    importResult.show = true;
    importResult.success = responseData.code === 200;
    importResult.title = responseData.code === 200 ? '导入完成' : '导入失败';
    importResult.message = responseData.message || '数据导入完成';
    importResult.totalCount = responseData.data?.totalCount || validCount.value;
    importResult.successCount = responseData.data?.successCount || 0;
    importResult.failCount = responseData.data?.failCount || 0;

    if (responseData.code === 200) {
      ElMessage.success(`成功导入 ${responseData.data?.successCount || 0} 条数据`);
    } else {
      ElMessage.error(responseData.message || '导入失败');
    }

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消导入
      return;
    }

    console.error('导入失败详细信息:', error);

    importResult.show = true;
    importResult.success = false;
    importResult.title = '导入失败';

    // 更详细的错误信息
    let errorMessage = '未知错误';
    let shouldRetry = false;

    if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status;
      if (status >= 500) {
        errorMessage = `服务器内部错误 (${status})，可能是数据处理量过大导致`;
        shouldRetry = true;
      } else if (status === 408 || status === 504) {
        errorMessage = `请求超时 (${status})，数据量较大，处理时间较长`;
        shouldRetry = true;
      } else {
        errorMessage = `服务器错误 (${status}): ${error.response.data?.message || error.response.statusText}`;
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，数据量较大，建议分批导入或稍后重试';
        shouldRetry = true;
      } else {
        errorMessage = '网络连接失败，请检查网络连接或后端服务是否正常运行';
        shouldRetry = true;
      }
    } else {
      // 请求配置错误
      errorMessage = `请求配置错误: ${error.message}`;
    }

    importResult.message = errorMessage;
    importResult.totalCount = validCount.value;
    importResult.successCount = 0;
    importResult.failCount = validCount.value;

    // 如果是可重试的错误，显示重试建议
    if (shouldRetry) {
      ElMessage({
        type: 'error',
        message: errorMessage + '\n建议：减少数据量或稍后重试',
        duration: 8000,
        showClose: true
      });
    } else {
      ElMessage.error(errorMessage);
    }
  } finally {
    importing.value = false;
  }
};

// 重置导入
const resetImport = () => {
  fileList.value = [];
  previewData.value = [];
  classesData.value = [];
  expandedClasses.value = [];
  importResult.show = false;
};
</script>

<style scoped>
/* 容器样式 */
.batch-import-list-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 卡片样式 */
.upload-card, .preview-card, .result-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.header-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 上传区域样式 */
.upload-area {
  text-align: center;
}

.upload-component {
  margin-bottom: 20px;
}

.upload-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 班级分组容器样式 */
.class-groups-container {
  margin: 20px 0;
}

/* 总体统计样式 */
.overall-summary {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.overall-summary :deep(.el-statistic__content) {
  color: white;
}

.overall-summary :deep(.el-statistic__head) {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

/* 班级分组样式 */
.class-groups {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.class-group-card {
  height: fit-content;
}

.class-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.class-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 班级卡片头部 */
.class-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.class-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.class-title h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.class-stats {
  display: flex;
  gap: 8px;
}

/* 班级信息样式 */
.class-info {
  margin-bottom: 20px;
}

/* 学生部分样式 */
.students-section {
  margin-top: 20px;
}

.students-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f2f5;
}

.students-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.students-table {
  margin-top: 12px;
}

/* 数据状态样式 */
.invalid-data {
  color: #f56c6c;
  font-weight: 500;
}

.warning-icon {
  margin-left: 4px;
  color: #e6a23c;
  font-size: 14px;
}

/* 空状态样式 */
.empty-students {
  padding: 40px 0;
  text-align: center;
}

/* 结果统计样式 */
.result-stats {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .class-groups {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .overall-summary {
    padding: 15px;
  }

  .class-card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .class-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .students-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>

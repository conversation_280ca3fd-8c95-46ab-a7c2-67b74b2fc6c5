package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
@TableName("operation_log")
public class OperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 操作类型(1=登录,2=登出,3=创建,4=更新,5=删除,6=查询,7=导入,8=导出,9=授权,10=审核,11=其他)
     */
    private Integer operationType;

    /**
     * 操作模块
     */
    private String module;

    /**
     * 操作描述
     */
    @TableField("`description`")
    private String description;

    /**
     * IP 地址
     */
    private String ip;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 操作状态（1-成功，2-失败，3-待审核）
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    // 分页参数
    @TableField(exist = false)
    private Integer pageSize;
    @TableField(exist = false)
    private Integer pageNum;

    // 时间范围查询参数
    @TableField(exist = false)
    private LocalDateTime startTime;
    @TableField(exist = false)
    private LocalDateTime endTime;
}

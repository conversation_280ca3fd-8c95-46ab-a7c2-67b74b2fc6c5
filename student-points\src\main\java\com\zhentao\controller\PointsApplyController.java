package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.dto.system.system.PointsApplyDto;
import com.zhentao.pojo.*;
import com.zhentao.service.*;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

import java.util.*;

/**
 * <p>
 * 积分变动申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/points-apply")
public class PointsApplyController {
    @Autowired
    PointsApplyService pointsApplyService;
    @Autowired
    EduStudentController eduStudentController;
    @Autowired
    EduStudentService eduStudentService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    EduClassService eduClassService;
    @Autowired
    PointsRecordService pointsRecordService;
    @Autowired
    OperationLogService operationLogService;

    // 查看登录学生的申请列表
    @PostMapping("/studentList")
    public Page<PointsApply> studentList(@RequestBody PointsApplyDto pointsApply) {
        Page<PointsApply> page = new Page<>(pointsApply.getPageNum(), pointsApply.getPageSize());
        QueryWrapper<PointsApply> wrapper = new QueryWrapper<>();
        wrapper.eq("student_no",pointsApply.getStudentNo());
        wrapper.eq(pointsApply.getPointsChange()!=null && pointsApply.getPointsChange()!=0,"points_change",pointsApply.getPointsChange());
        // 查询在某段时间内的申请
        if (pointsApply.getStartTime() != null && pointsApply.getEndTime() != null) {
            wrapper.between("create_time", pointsApply.getStartTime(), pointsApply.getEndTime());
        }
        Page<PointsApply> page1 = pointsApplyService.page(page, wrapper);
        for (PointsApply record : page1.getRecords()) {
            // 获取操作人姓名
            SysUser byId = sysUserService.getById(record.getApplyUserId());
            record.setApplyUser(byId);

        }
        return page1;
    }


    //院长查询
    @RequestMapping("/YZList0")
    public Result YZList(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status2",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null&&pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq("status1",2);//只有主任通过后才能被院长查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/YZList1")
    public Result YZList1(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.ne("status2",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null&&pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus2()!=null&&pointsApply1.getStatus2()!=0,"status2",pointsApply1.getStatus2());
        queryWrapper.eq("status1",2);//只有主任通过后才能被院长查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/YZList2")
    public Result YZList2(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null&&pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus2()!=null && pointsApply1.getStatus2()!=0,"status2",pointsApply1.getStatus2());
        queryWrapper.eq("status1",2);//只有主任通过后才能被院长查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }



















    //专业主任查询
    @RequestMapping("/YZRList0")
    public Result YZYList0(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null&&pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq("status",2);//只有导员讲师通过后才能被主任查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/YZRList1")
    public Result YZYList1(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.ne("status1",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus1()!=null && pointsApply1.getStatus1()!=0,"status1",pointsApply1.getStatus1());
        queryWrapper.eq("status",2);//只有导员讲师通过后才能被主任查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }

        return list!=null?Result.OK(list):Result.ERROR();

    }
    @RequestMapping("/YZRList2")
    public Result YZYList2(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus1()!=null && pointsApply1.getStatus1()!=0,"status1",pointsApply1.getStatus1());
        queryWrapper.eq("status",2);//只有导员讲师通过后才能被主任查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }





    //专高主任查询
    @RequestMapping("/GZRList0")
    public Result GZYList0(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status1",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null&&pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq("status",2);//只有导员讲师通过后才能被主任查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/GZRList1")
    public Result GZYList1(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.ne("status1",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus1()!=null && pointsApply1.getStatus1()!=0,"status1",pointsApply1.getStatus1());
        queryWrapper.eq("status",2);//只有导员讲师通过后才能被主任查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }

        return list!=null?Result.OK(list):Result.ERROR();

    }
    @RequestMapping("/GZRList2")
    public Result GZYList2(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus1()!=null && pointsApply1.getStatus1()!=0,"status1",pointsApply1.getStatus1());
        queryWrapper.eq("status",2);//只有导员讲师通过后才能被主任查看
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }



    //导员查询本班信息
    @RequestMapping("/DYList0")
    public Result DYList0(@RequestBody PointsApply pointsApply1){
        SysUser user=sysUserService.getById(UserContext.getCurrentUser().getUserId());
        QueryWrapper<EduClass> classQueryWrapper=new QueryWrapper<>();
        classQueryWrapper.eq("counselor_id",user.getUserId());
        List<EduClass> eduClass=eduClassService.list(classQueryWrapper);
        List<Integer> ids=new ArrayList<>();
        for (EduClass eduClass1 : eduClass){
            ids.add(eduClass1.getClassId());
        }
        
        // 修复：当用户没有管理的班级时，返回空列表而不是执行空IN查询
        if (ids.isEmpty()) {
            Page<PointsApply> emptyPage = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            return Result.OK(emptyPage);
        }
        
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.in("class_id",ids);
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/DYList1")
    public Result DYList1(@RequestBody PointsApply pointsApply1){
        SysUser user=sysUserService.getById(UserContext.getCurrentUser().getUserId());
        QueryWrapper<EduClass> classQueryWrapper=new QueryWrapper<>();
        classQueryWrapper.eq("counselor_id",user.getUserId());
        List<EduClass> eduClass=eduClassService.list(classQueryWrapper);
        List<Integer> ids=new ArrayList<>();
        for (EduClass eduClass1 : eduClass){
            ids.add(eduClass1.getClassId());
        }
        
        // 修复：当用户没有管理的班级时，返回空列表而不是执行空IN查询
        if (ids.isEmpty()) {
            Page<PointsApply> emptyPage = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            return Result.OK(emptyPage);
        }
        
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.ne("status",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getStatus()!=null && pointsApply1.getStatus()!=0,"status",pointsApply1.getStatus());
        queryWrapper.in("class_id",ids);
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/DYList2")
    public Result DYList2(@RequestBody PointsApply pointsApply1){
        SysUser user=sysUserService.getById(UserContext.getCurrentUser().getUserId());
        QueryWrapper<EduClass> classQueryWrapper=new QueryWrapper<>();
        classQueryWrapper.eq("counselor_id",user.getUserId());
        List<EduClass> eduClass=eduClassService.list(classQueryWrapper);
        List<Integer> ids=new ArrayList<>();
        for (EduClass eduClass1 : eduClass){
            ids.add(eduClass1.getClassId());
        }
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getStatus()!=null && pointsApply1.getStatus()!=0,"status",pointsApply1.getStatus());
        queryWrapper.in("class_id",ids);
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }

    //讲师查询本班信息
    @RequestMapping("/JSList0")
    public Result JSList0(@RequestBody PointsApply pointsApply1){
        SysUser user=sysUserService.getById(UserContext.getCurrentUser().getUserId());
        QueryWrapper<EduClass> classQueryWrapper=new QueryWrapper<>();
        classQueryWrapper.eq("teacher_id",user.getUserId());
        List<EduClass> eduClass=eduClassService.list(classQueryWrapper);
        List<Integer> ids=new ArrayList<>();
        for (EduClass eduClass1 : eduClass){
            ids.add(eduClass1.getClassId());
        }
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.in("class_id",ids);
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/JSList1")
    public Result JSList1(@RequestBody PointsApply pointsApply1){
        SysUser user=sysUserService.getById(UserContext.getCurrentUser().getUserId());
        QueryWrapper<EduClass> classQueryWrapper=new QueryWrapper<>();
        classQueryWrapper.eq("teacher_id",user.getUserId());
        List<EduClass> eduClass=eduClassService.list(classQueryWrapper);
        List<Integer> ids=new ArrayList<>();
        for (EduClass eduClass1 : eduClass){
            ids.add(eduClass1.getClassId());
        }
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.ne("status",1);
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getStatus()!=null && pointsApply1.getStatus()!=0,"status",pointsApply1.getStatus());
        queryWrapper.in("class_id",ids);
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/JSList2")
    public Result JSList2(@RequestBody PointsApply pointsApply1){
        SysUser user=sysUserService.getById(UserContext.getCurrentUser().getUserId());
        QueryWrapper<EduClass> classQueryWrapper=new QueryWrapper<>();
        classQueryWrapper.eq("teacher_id",user.getUserId());
        List<EduClass> eduClass=eduClassService.list(classQueryWrapper);
        List<Integer> ids=new ArrayList<>();
        for (EduClass eduClass1 : eduClass){
            ids.add(eduClass1.getClassId());
        }
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.orderByDesc("apply_id");
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getStatus()!=null && pointsApply1.getStatus()!=0,"status",pointsApply1.getStatus());
        queryWrapper.in("class_id",ids);
        Page<PointsApply> page = new Page<>(pointsApply1.getPageNum(), pointsApply1.getPageSize());
        Page<PointsApply> list=pointsApplyService.page(page,queryWrapper);
        for (PointsApply pointsApply:list.getRecords()){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            pointsApply.setStudent(byId);
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }







    @RequestMapping("/class")
    public Result findClass(){
        List<EduClass> list=eduClassService.list();
        return list!=null?Result.OK(list):Result.ERROR();
    }
    //导员讲师通过&拒绝审批
    @RequestMapping("/DYJStg")
    public Result DYJStg(Integer id, HttpServletRequest request){
       PointsApply pointsApply=pointsApplyService.getById(id);
       pointsApply.setStatus(2);
       pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
       pointsApply.setReviewTime(new Date());
        boolean b = pointsApplyService.updateById(pointsApply);

        // 记录操作日志
        try {
            String username = "unknown";
            String realName = "未知用户";
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();
            }

            String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
            String operationDescription = String.format("导员讲师审核通过：申请ID=%d，学号=%s，%s%d分",
                id, pointsApply.getStudentNo(), operationType, pointsApply.getPoints());

            operationLogService.recordLog(username, realName, 10, "points",
                operationDescription, b ? 1 : 2, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return b?Result.OK():Result.ERROR();
    }
    @RequestMapping("/DYJSjj")
    public Result DYJSjj(Integer id, HttpServletRequest request){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus(3);
        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());
        boolean b = pointsApplyService.updateById(pointsApply);

        // 记录操作日志
        try {
            String username = "unknown";
            String realName = "未知用户";
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();
            }

            String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
            String operationDescription = String.format("导员讲师审核拒绝：申请ID=%d，学号=%s，%s%d分",
                id, pointsApply.getStudentNo(), operationType, pointsApply.getPoints());

            operationLogService.recordLog(username, realName, 10, "points",
                operationDescription, b ? 1 : 2, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return b?Result.OK():Result.ERROR();
    }

    //主任通过&拒绝审批
    @RequestMapping("/ZRtg")
    public Result ZRtg(Integer id, HttpServletRequest request){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus1(2);
        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());
        boolean b = pointsApplyService.updateById(pointsApply);

        // 记录操作日志
        try {
            String username = "unknown";
            String realName = "未知用户";
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();
            }

            String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
            String operationDescription = String.format("主任审核通过：申请ID=%d，学号=%s，%s%d分",
                id, pointsApply.getStudentNo(), operationType, pointsApply.getPoints());

            operationLogService.recordLog(username, realName, 10, "points",
                operationDescription, b ? 1 : 2, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return b?Result.OK():Result.ERROR();
    }
    @RequestMapping("/ZRjj")
    public Result ZRjj(Integer id, HttpServletRequest request){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus1(3);
        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());
        boolean b = pointsApplyService.updateById(pointsApply);

        // 记录操作日志
        try {
            String username = "unknown";
            String realName = "未知用户";
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();
            }

            String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
            String operationDescription = String.format("主任审核拒绝：申请ID=%d，学号=%s，%s%d分",
                id, pointsApply.getStudentNo(), operationType, pointsApply.getPoints());

            operationLogService.recordLog(username, realName, 10, "points",
                operationDescription, b ? 1 : 2, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return b?Result.OK():Result.ERROR();
    }
    //院长通过&拒绝审批
    @RequestMapping("/YZtg")
    public Result YZtg(Integer id, HttpServletRequest request){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus2(2);

        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());

        if (pointsApply.getPointsChange()==1){
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            if(byId.getDelFlag().equals("1")){
                return Result.ERROR("该学生已删除，加分失败");
            }
            if (byId.getStatus().equals("1")){
                return Result.ERROR("该学生已休学，加分失败");
            }
            if (byId.getStatus().equals("2")){
                return Result.ERROR("该学生已退学，加分失败");
            }
            if (byId.getStatus().equals("3")){
                return Result.ERROR("该学生已毕业，加分失败");
            }
            Integer points=byId.getPoints()+pointsApply.getPoints();
            PointsRecord pointsRecord=new PointsRecord();
            pointsRecord.setStudentNo(pointsApply.getStudentNo());
            pointsRecord.setPointsBefore(byId.getPoints());
            pointsRecord.setPointsChange(pointsApply.getPointsChange());
            pointsRecord.setPointsAfter(points);
            pointsRecord.setApplyId(pointsApply.getApplyId());
            pointsRecord.setOperatorId(UserContext.getCurrentUser().getUserId());
            pointsRecord.setOperationType(3);
            pointsRecord.setOperationTime(new Date());
            pointsRecord.setReason(pointsApply.getReason());
            pointsRecord.setCreateBy(UserContext.getCurrentUser().getUserId());
            pointsRecord.setCreateTime(new Date());
            pointsRecord.setDelFlag(1);
            pointsRecordService.save(pointsRecord);

            byId.setPoints(points);
            byId.setUpdateBy(UserContext.getCurrentUser().getUserId());
            byId.setUpdateTime(new Date());
            boolean b = eduStudentService.updateById(byId);
        }else{
            QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
            eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
            EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);
            if(byId.getDelFlag().equals("1")){
                return Result.ERROR("该学生已删除，减分失败");
            }
            if (byId.getStatus().equals("1")){
                return Result.ERROR("该学生已休学，减分失败");
            }
            if (byId.getStatus().equals("2")){
                return Result.ERROR("该学生已退学，减分失败");
            }
            if (byId.getStatus().equals("3")){
                return Result.ERROR("该学生已毕业，减分失败");
            }


            byId.setPoints(byId.getPoints()-pointsApply.getPoints());
            byId.setUpdateBy(UserContext.getCurrentUser().getUserId());
            byId.setUpdateTime(new Date());
            boolean b = eduStudentService.updateById(byId);
        }

        boolean b = pointsApplyService.updateById(pointsApply);

        // 记录操作日志
        try {
            String username = "unknown";
            String realName = "未知用户";
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();
            }

            String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
            String operationDescription = String.format("院长审核通过：申请ID=%d，学号=%s，%s%d分",
                id, pointsApply.getStudentNo(), operationType, pointsApply.getPoints());

            operationLogService.recordLog(username, realName, 10, "points",
                operationDescription, b ? 1 : 2, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return b?Result.OK():Result.ERROR();
    }
    @RequestMapping("/YZjj")
    public Result YZjj(Integer id, HttpServletRequest request){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus2(3);
        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());
        boolean b = pointsApplyService.updateById(pointsApply);

        QueryWrapper<EduStudent> eduStudentQueryWrapper=new QueryWrapper<>();
        eduStudentQueryWrapper.eq("student_no",pointsApply.getStudentNo());
        EduStudent byId = eduStudentService.getOne(eduStudentQueryWrapper);

        Integer points=byId.getPoints()+pointsApply.getPoints();
        PointsRecord pointsRecord=new PointsRecord();
        pointsRecord.setStudentNo(pointsApply.getStudentNo());
        pointsRecord.setPointsBefore(byId.getPoints());
        pointsRecord.setPointsChange(pointsApply.getPointsChange());
        pointsRecord.setPointsAfter(points);
        pointsRecord.setApplyId(pointsApply.getApplyId());
        pointsRecord.setOperatorId(UserContext.getCurrentUser().getUserId());
        pointsRecord.setOperationType(4);
        pointsRecord.setOperationTime(new Date());
        pointsRecord.setReason(pointsApply.getReason());
        pointsRecord.setCreateBy(UserContext.getCurrentUser().getUserId());
        pointsRecord.setCreateTime(new Date());
        pointsRecord.setDelFlag(1);
        pointsRecordService.save(pointsRecord);

        // 记录操作日志
        try {
            String username = "unknown";
            String realName = "未知用户";
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();
            }

            String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
            String operationDescription = String.format("院长审核拒绝：申请ID=%d，学号=%s，%s%d分",
                id, pointsApply.getStudentNo(), operationType, pointsApply.getPoints());

            operationLogService.recordLog(username, realName, 10, "points",
                operationDescription, b ? 1 : 2, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return b?Result.OK():Result.ERROR();
    }

    // ================= 学生端申请管理接口 =================

    /**
     * 学生提交积分申请（加分或扣分）
     * @param pointsApply 申请信息
     * @param request HTTP请求
     * @return 操作结果
     */
    @RequestMapping("/studentApply")
    public Result studentApply(@RequestBody PointsApply pointsApply, HttpServletRequest request) {
        try {
            // 获取当前用户信息
            String username = "unknown";
            String realName = "未知用户";
            Integer currentUserId = null;
            try {
                if (UserContext.getCurrentUser() != null) {
                    currentUserId = UserContext.getCurrentUser().getUserId();
                    username = UserContext.getCurrentUser().getUsername();
                    realName = UserContext.getCurrentUser().getRealName();

                    // 如果realName为空，从数据库重新获取
                    if (realName == null || realName.trim().isEmpty()) {
                        SysUser userFromDb = sysUserService.getById(currentUserId);
                        if (userFromDb != null && userFromDb.getRealName() != null) {
                            realName = userFromDb.getRealName();
                        }
                    }
                }
            } catch (Exception e) {
                System.err.println("获取用户信息失败: " + e.getMessage());
            }

            // 临时测试：如果用户未登录，使用默认值
            if (currentUserId == null) {
                System.out.println("用户未登录，使用测试默认值");
                currentUserId = 1; // 使用默认用户ID进行测试
                username = "test_student";
                realName = "测试学生";
            }

            // 设置申请基本信息
            pointsApply.setApplyUserId(currentUserId);
            pointsApply.setCreateBy(currentUserId);
            pointsApply.setCreateTime(new Date());
            pointsApply.setDelFlag(1);

            // 根据用户角色设置审核状态
            // 学生/秘书申请需要经过完整的审核流程
            pointsApply.setStatus(1);   // 导员讲师待审核
            pointsApply.setStatus1(1);  // 主任待审核
            pointsApply.setStatus2(1);  // 院长待审核

            // 调用服务层方法
            Result result = pointsApplyService.addJiFen(pointsApply);

            // 记录操作日志
            try {
                String operationDescription;
                Integer operationType;
                Integer status = result.getCode() == 200 ? 1 : 2; // 1-成功，2-失败

                if (pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2) {
                    // 减分申请
                    operationType = 5; // 5-删除（扣分）
                    operationDescription = String.format("学生积分扣除申请：学号=%s，扣除积分=%d分，原因=%s",
                        pointsApply.getStudentNo(),
                        pointsApply.getPoints(),
                        pointsApply.getReason());
                } else {
                    // 加分申请
                    operationType = 3; // 3-创建（加分）
                    operationDescription = String.format("学生积分添加申请：学号=%s，添加积分=%d分，原因=%s",
                        pointsApply.getStudentNo(),
                        pointsApply.getPoints(),
                        pointsApply.getReason());
                }

                operationLogService.recordLog(username, realName, operationType, "points",
                    operationDescription, status, request);
            } catch (Exception e) {
                System.err.println("记录操作日志失败: " + e.getMessage());
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("提交申请失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生的申请记录列表
     * @param params 查询参数
     * @return 申请记录列表
     */
    @RequestMapping("/studentApplicationList")
    public Result studentApplicationList(@RequestBody(required = false) Map<String, Object> params) {
        try {
            // 获取当前用户ID
            Integer currentUserId = null;
            try {
                if (UserContext.getCurrentUser() != null) {
                    currentUserId = UserContext.getCurrentUser().getUserId();
                }
            } catch (Exception e) {
                System.err.println("获取用户信息失败: " + e.getMessage());
            }

            // 临时测试：如果用户未登录，使用默认值
            if (currentUserId == null) {
                System.out.println("用户未登录，使用测试默认用户ID");
                currentUserId = 1; // 使用默认用户ID进行测试
            }

            // 构建查询条件
            QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("apply_user_id", currentUserId);
            queryWrapper.eq("del_flag", 1);

            // 处理筛选条件
            if (params != null) {
                // 状态筛选
                if (params.containsKey("status") && params.get("status") != null && !params.get("status").toString().trim().isEmpty()) {
                    String statusFilter = params.get("status").toString();
                    switch (statusFilter) {
                        case "pending":
                            // 待审核：任一级别为待审核状态
                            queryWrapper.and(wrapper -> wrapper
                                .eq("status", 1)
                                .or().eq("status1", 1)
                                .or().eq("status2", 1));
                            break;
                        case "approved":
                            // 已通过：所有级别都通过
                            queryWrapper.eq("status", 2).eq("status1", 2).eq("status2", 2);
                            break;
                        case "rejected":
                            // 已拒绝：任一级别被拒绝
                            queryWrapper.and(wrapper -> wrapper
                                .eq("status", 3)
                                .or().eq("status1", 3)
                                .or().eq("status2", 3));
                            break;
                    }
                }

                // 类型筛选
                if (params.containsKey("pointsChange") && params.get("pointsChange") != null) {
                    queryWrapper.eq("points_change", params.get("pointsChange"));
                }

                // 时间范围筛选
                if (params.containsKey("startDate") && params.get("startDate") != null) {
                    queryWrapper.ge("create_time", params.get("startDate"));
                }
                if (params.containsKey("endDate") && params.get("endDate") != null) {
                    queryWrapper.le("create_time", params.get("endDate"));
                }
            }

            // 分页参数
            int pageNum = 1;
            int pageSize = 10;
            if (params != null) {
                if (params.containsKey("pageNum")) {
                    pageNum = Integer.parseInt(params.get("pageNum").toString());
                }
                if (params.containsKey("pageSize")) {
                    pageSize = Integer.parseInt(params.get("pageSize").toString());
                }
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 执行分页查询
            Page<PointsApply> page = new Page<>(pageNum, pageSize);
            Page<PointsApply> result = pointsApplyService.page(page, queryWrapper);

            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询申请记录失败: " + e.getMessage());
        }
    }

    // -------------------------------------------------------

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @RequestMapping("addJiFen")
    public Result addJiFen(@RequestBody PointsApply pointsApply, HttpServletRequest request){
        // 添加调试日志
        System.out.println("接收到的积分申请数据: " + pointsApply);
        System.out.println("积分变动值: " + pointsApply.getPointsChange());

        // 获取当前用户信息
        String username = "unknown";
        String realName = "未知用户";
        try {
            if (UserContext.getCurrentUser() != null) {
                username = UserContext.getCurrentUser().getUsername();
                realName = UserContext.getCurrentUser().getRealName();

                // 如果realName为空，从数据库重新获取
                if (realName == null || realName.trim().isEmpty()) {
                    Integer userId = UserContext.getCurrentUser().getUserId();
                    if (userId != null) {
                        SysUser userFromDb = sysUserService.getById(userId);
                        if (userFromDb != null && userFromDb.getRealName() != null) {
                            realName = userFromDb.getRealName();
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
        }

        // 调用服务层方法
        Result result = pointsApplyService.addJiFen(pointsApply);

        // 记录操作日志
        try {
            String operationDescription;
            Integer operationType;
            Integer status = result.getCode() == 200 ? 1 : 2; // 1-成功，2-失败

            if (pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2) {
                // 减分操作
                operationType = 5; // 5-删除（扣分）
                operationDescription = String.format("学生积分扣除申请：学号=%s，扣除积分=%d分，原因=%s",
                    pointsApply.getStudentNo(),
                    pointsApply.getPoints(),
                    pointsApply.getReason());
            } else {
                // 加分操作
                operationType = 3; // 3-创建（加分）
                operationDescription = String.format("学生积分添加申请：学号=%s，添加积分=%d分，原因=%s",
                    pointsApply.getStudentNo(),
                    pointsApply.getPoints(),
                    pointsApply.getReason());
            }

            operationLogService.recordLog(username, realName, operationType, "points",
                operationDescription, status, request);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }

        return result;
    }

    @RequestMapping("list")
    public Result list(@RequestBody(required = false) Map<String, Object> params) {
        return pointsApplyService.listPointsApplies(params);
    }

    /**
     * 获取积分历史记录，支持多种过滤条件
     * @param pointsApply 过滤参数，可包含：
     *               - studentId: 学生ID
     *               - className: 班级名称
     *               - pointsChange: 积分类型（1-加分，2-减分）
     *               - category: 积分项目类别
     *               - status: 状态（1-待审核，2-已通过，3-已拒绝）
     *               - startDate: 开始日期
     *               - endDate: 结束日期
     * @return 积分历史记录列表
     */
    @RequestMapping("history")
    public Result getPointsHistory(@RequestBody(required = false) Object param) {
        try {
            System.out.println("接收到的参数类型: " + (param != null ? param.getClass().getName() : "null"));
            System.out.println("接收到的参数内容: " + param);

            // 处理不同类型的参数
            if (param instanceof PointsApply) {
                // 如果是PointsApply对象，直接传递
                PointsApply pointsApply = (PointsApply) param;
                // 设置默认分页参数
                if (pointsApply.getPageNum() == null) {
                    pointsApply.setPageNum(1);
                }
                if (pointsApply.getPageSize() == null) {
                    pointsApply.setPageSize(10);
                }
                return pointsApplyService.getPointsHistory(pointsApply);
            } else if (param instanceof Map) {
                // 如果是Map对象，转换为PointsApply对象
                @SuppressWarnings("unchecked")
                Map<String, Object> paramMap = (Map<String, Object>) param;
                PointsApply pointsApply = new PointsApply();

                // 设置分页参数
                pointsApply.setPageNum(paramMap.containsKey("pageNum") ? parseInteger(paramMap.get("pageNum")) : 1);
                pointsApply.setPageSize(paramMap.containsKey("pageSize") ? parseInteger(paramMap.get("pageSize")) : 10);

                // 设置各个字段
                if (paramMap.containsKey("studentNo")) {
                    pointsApply.setStudentNo((String) paramMap.get("studentNo"));
                }
                if (paramMap.containsKey("classId")) {
                    pointsApply.setClassId(parseInteger(paramMap.get("classId")));
                }
                if (paramMap.containsKey("className")) {
                    // 班级名称需要特殊处理，存储在临时字段中
                    pointsApply.setRemark((String) paramMap.get("className"));
                }
                if (paramMap.containsKey("pointsChange")) {
                    pointsApply.setPointsChange(parseInteger(paramMap.get("pointsChange")));
                }
                if (paramMap.containsKey("status")) {
                    pointsApply.setStatus(parseInteger(paramMap.get("status")));
                }
                if (paramMap.containsKey("category")) {
                    // 类别需要特殊处理，存储在临时字段中
                    pointsApply.setReason((String) paramMap.get("category"));
                }

                // 处理日期范围 - 修复日期转换
                try {
                    if (paramMap.containsKey("startTime")) {
                        Object startTimeObj = paramMap.get("startTime");
                        if (startTimeObj instanceof String) {
                            // 如果是字符串，需要转换为Date
                            pointsApply.setStartTime(parseDate((String) startTimeObj));
                        } else if (startTimeObj instanceof Date) {
                            pointsApply.setStartTime((Date) startTimeObj);
                        }
                    }

                    if (paramMap.containsKey("endTime")) {
                        Object endTimeObj = paramMap.get("endTime");
                        if (endTimeObj instanceof String) {
                            // 如果是字符串，需要转换为Date
                            pointsApply.setEndTime(parseDate((String) endTimeObj));
                        } else if (endTimeObj instanceof Date) {
                            pointsApply.setEndTime((Date) endTimeObj);
                        }
                    }
                } catch (Exception e) {
                    System.out.println("日期转换错误: " + e.getMessage());
                    // 如果日期转换失败，设置为null
                    pointsApply.setStartTime(null);
                    pointsApply.setEndTime(null);
                }

                return pointsApplyService.getPointsHistory(pointsApply);
            } else {
                // 如果是null或其他类型，传递空对象
                PointsApply pointsApply = new PointsApply();
                pointsApply.setPageNum(1);
                pointsApply.setPageSize(10);
                return pointsApplyService.getPointsHistory(pointsApply);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 解析日期字符串为Date对象
     * 支持多种格式的日期字符串
     */
    private Date parseDate(String dateStr) throws Exception {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        // 处理ISO格式的日期字符串 (2025-07-14T16:00:00.000Z)
        if (dateStr.contains("T")) {
            // 移除末尾的Z并替换T为空格
            dateStr = dateStr.replace("T", " ").replace("Z", "");
            // 如果有毫秒，保留3位
            if (dateStr.contains(".")) {
                int dotIndex = dateStr.indexOf(".");
                String beforeDot = dateStr.substring(0, dotIndex);
                String afterDot = dateStr.substring(dotIndex + 1);
                if (afterDot.length() > 3) {
                    afterDot = afterDot.substring(0, 3);
                }
                dateStr = beforeDot + "." + afterDot;
            }
        }

        // 尝试多种日期格式
        String[] dateFormats = {
                "yyyy-MM-dd HH:mm:ss.SSS",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd"
        };

        for (String format : dateFormats) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(format);
                return sdf.parse(dateStr);
            } catch (Exception e) {
                // 尝试下一个格式
                continue;
            }
        }

        // 所有格式都失败，抛出异常
        throw new Exception("无法解析日期: " + dateStr);
    }

    /**
     * 解析整数值，处理不同类型的输入
     */
    private Integer parseInteger(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (value instanceof Long) {
            return ((Long) value).intValue();
        } else if (value instanceof Double) {
            return ((Double) value).intValue();
        }

        return null;
    }

    /**
     * 获取积分统计信息
     * @param params 过滤参数，可包含：
     *               - studentId: 学生ID
     *               - classId: 班级ID
     *               - startDate: 开始日期
     *               - endDate: 结束日期
     * @return 积分统计信息
     */
    @RequestMapping("statistics")
    public Result getPointsStatistics(@RequestBody(required = false) Map<String, Object> params) {
        return pointsApplyService.getPointsStatistics(params);
    }

    /**
     * 撤销积分申请
     * @param params 包含applyId的参数Map
     * @return 操作结果
     */
    @RequestMapping("cancel")
    public Result cancelPointsApplication(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            if (params == null || !params.containsKey("applyId")) {
                return Result.ERROR("申请ID不能为空");
            }

            Integer applyId = parseInteger(params.get("applyId"));
            if (applyId == null) {
                return Result.ERROR("无效的申请ID");
            }

            // 获取当前用户信息
            String username = "unknown";
            String realName = "未知用户";
            try {
                if (UserContext.getCurrentUser() != null) {
                    username = UserContext.getCurrentUser().getUsername();
                    realName = UserContext.getCurrentUser().getRealName();
                }
            } catch (Exception e) {
                System.err.println("获取用户信息失败: " + e.getMessage());
            }

            // 在撤销前获取申请信息用于日志记录
            PointsApply pointsApply = null;
            try {
                pointsApply = pointsApplyService.getById(applyId);
            } catch (Exception e) {
                System.err.println("获取申请信息失败: " + e.getMessage());
            }

            // 调用服务层方法
            Result result = pointsApplyService.cancelPointsApplication(applyId);

            // 记录操作日志
            try {
                Integer status = result.getCode() == 200 ? 1 : 2; // 1-成功，2-失败
                String operationDescription;

                if (pointsApply != null) {
                    String operationType = pointsApply.getPointsChange() != null && pointsApply.getPointsChange() == 2 ? "扣分" : "加分";
                    operationDescription = String.format("撤销积分申请：申请ID=%d，学号=%s，%s%d分",
                        applyId,
                        pointsApply.getStudentNo(),
                        operationType,
                        pointsApply.getPoints());
                } else {
                    operationDescription = String.format("撤销积分申请：申请ID=%d", applyId);
                }

                operationLogService.recordLog(username, realName, 5, "points",
                    operationDescription, status, request);
            } catch (Exception e) {
                System.err.println("记录操作日志失败: " + e.getMessage());
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("撤销申请失败: " + e.getMessage());
        }
    }

    /**
     * 调试端点：直接查询数据库中的积分申请记录
     * @return 积分申请记录列表
     */
    @RequestMapping("debug")
    public Result debug() {
        try {
            // 直接执行SQL查询
            String sql = "SELECT * FROM points_apply LIMIT 10";
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sql);

            // 输出记录数量和第一条记录
            System.out.println("查询到 " + records.size() + " 条记录");
            if (!records.isEmpty()) {
                System.out.println("第一条记录: " + records.get(0));
            }

            // 查询表结构
            String schemaSql = "SHOW COLUMNS FROM points_apply";
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(schemaSql);
            System.out.println("表结构: " + columns);

            // 查询积分添加记录（包含del_flag条件）
            String addSql = "SELECT * FROM points_apply WHERE points_change = 1 AND del_flag = 1 LIMIT 10";
            List<Map<String, Object>> addRecords = jdbcTemplate.queryForList(addSql);
            System.out.println("积分添加记录(del_flag=1): " + addRecords.size() + " 条");

            // 查询积分扣除记录（包含del_flag条件）
            String deductSql = "SELECT * FROM points_apply WHERE points_change = 2 AND del_flag = 1 LIMIT 10";
            List<Map<String, Object>> deductRecords = jdbcTemplate.queryForList(deductSql);
            System.out.println("积分扣除记录(del_flag=1): " + deductRecords.size() + " 条");

            // 查询所有积分记录（不含del_flag条件）
            String allAddSql = "SELECT * FROM points_apply WHERE points_change = 1 LIMIT 10";
            List<Map<String, Object>> allAddRecords = jdbcTemplate.queryForList(allAddSql);
            System.out.println("所有积分添加记录: " + allAddRecords.size() + " 条");

            String allDeductSql = "SELECT * FROM points_apply WHERE points_change = 2 LIMIT 10";
            List<Map<String, Object>> allDeductRecords = jdbcTemplate.queryForList(allDeductSql);
            System.out.println("所有积分扣除记录: " + allDeductRecords.size() + " 条");

            // 检查del_flag字段的值
            String delFlagSql = "SELECT del_flag, COUNT(*) as count FROM points_apply GROUP BY del_flag";
            List<Map<String, Object>> delFlagStats = jdbcTemplate.queryForList(delFlagSql);
            System.out.println("del_flag字段统计: " + delFlagStats);

            // 查询del_flag为'1'的记录（1-存在）
            String delFlag1Sql = "SELECT * FROM points_apply WHERE del_flag = '1' LIMIT 5";
            List<Map<String, Object>> delFlag1Records = jdbcTemplate.queryForList(delFlag1Sql);
            System.out.println("del_flag='1'的记录: " + delFlag1Records.size() + " 条");

            // 查询del_flag为1(数字)的记录
            String delFlagNum1Sql = "SELECT * FROM points_apply WHERE del_flag = 1 LIMIT 5";
            List<Map<String, Object>> delFlagNum1Records = jdbcTemplate.queryForList(delFlagNum1Sql);
            System.out.println("del_flag=1(数字)的记录: " + delFlagNum1Records.size() + " 条");

            // 查询del_flag为'0'的记录
            String delFlag0Sql = "SELECT * FROM points_apply WHERE del_flag = '0' LIMIT 5";
            List<Map<String, Object>> delFlag0Records = jdbcTemplate.queryForList(delFlag0Sql);
            System.out.println("del_flag='0'的记录: " + delFlag0Records.size() + " 条");

            // 查询del_flag为0(数字)的记录
            String delFlagNum0Sql = "SELECT * FROM points_apply WHERE del_flag = 0 LIMIT 5";
            List<Map<String, Object>> delFlagNum0Records = jdbcTemplate.queryForList(delFlagNum0Sql);
            System.out.println("del_flag=0(数字)的记录: " + delFlagNum0Records.size() + " 条");

            // 查询del_flag为NULL的记录
            String delFlagNullSql = "SELECT * FROM points_apply WHERE del_flag IS NULL LIMIT 5";
            List<Map<String, Object>> delFlagNullRecords = jdbcTemplate.queryForList(delFlagNullSql);
            System.out.println("del_flag为NULL的记录: " + delFlagNullRecords.size() + " 条");

            // 测试不加del_flag条件的查询
            String noDelFlagSql = "SELECT * FROM points_apply LIMIT 5";
            List<Map<String, Object>> noDelFlagRecords = jdbcTemplate.queryForList(noDelFlagSql);
            System.out.println("不加del_flag条件的记录: " + noDelFlagRecords.size() + " 条");

            // 专门查看points_change字段的值分布
            String pointsChangeSql = "SELECT points_change, COUNT(*) as count FROM points_apply GROUP BY points_change";
            List<Map<String, Object>> pointsChangeStats = jdbcTemplate.queryForList(pointsChangeSql);
            System.out.println("points_change字段值分布: " + pointsChangeStats);

            // 查看具体的points_change值
            String sampleSql = "SELECT apply_id, student_no, points_change, points, reason FROM points_apply LIMIT 10";
            List<Map<String, Object>> sampleRecords = jdbcTemplate.queryForList(sampleSql);
            System.out.println("样本记录: " + sampleRecords);

            // 返回所有查询结果
            Map<String, Object> result = new HashMap<>();
            result.put("allRecords", records);
            result.put("addRecords", addRecords);
            result.put("deductRecords", deductRecords);
            result.put("allAddRecords", allAddRecords);
            result.put("allDeductRecords", allDeductRecords);
            result.put("tableStructure", columns);
            result.put("delFlagStats", delFlagStats);
            result.put("delFlag1Records", delFlag1Records);
            result.put("delFlagNum1Records", delFlagNum1Records);
            result.put("delFlag0Records", delFlag0Records);
            result.put("delFlagNum0Records", delFlagNum0Records);
            result.put("delFlagNullRecords", delFlagNullRecords);
            result.put("noDelFlagRecords", noDelFlagRecords);
            result.put("pointsChangeStats", pointsChangeStats);
            result.put("sampleRecords", sampleRecords);

            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询失败: " + e.getMessage());
        }
    }
//------------------------------------------------虚线一下都是cmy写的-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    /**
     * 查询所有当天学生的一个加分记录
     */
    @PostMapping("/queryTodayAddPoints")
    public Result queryTodayAddPoints(){
        return pointsApplyService.queryTodayAddPoints();
    }

    /**
     * 查询所有当天学生一个减分记录
     */
    @PostMapping("/queryTodayMinusPoints")
    public Result queryTodayMinusPoints(){
        return pointsApplyService.queryTodayMinusPoints();
    }

}

import request from "@/request/index.js";
export function YZlist2(data){
    return request({
        url:"/points-apply/YZList2",
        method:"post",
        data:data
    })
}
export function GZRlist2(data){
    return request({
        url:"/points-apply/GZRList2",
        method:"post",
        data:data
    })
}
export function YZRlist2(data){
    return request({
        url:"/points-apply/YZRList2",
        method:"post",
        data:data
    })
}
export function JSlist2(data){
    return request({
        url:"/points-apply/JSList2",
        method:"post",
        data:data
    })
}
export function DYlist2(data){
    return request({
        url:"/points-apply/DYList2",
        method:"post",
        data:data
    })
}


export function YZlist1(data){
    return request({
        url:"/points-apply/YZList1",
        method:"post",
        data:data
    })
}
export function GZRlist1(data){
    return request({
        url:"/points-apply/GZRList1",
        method:"post",
        data:data
    })
}
export function YZRlist1(data){
    return request({
        url:"/points-apply/YZRList1",
        method:"post",
        data:data
    })
}
export function DYlist1(data){
    return request({
        url:"/points-apply/DYList1",
        method:"post",
        data:data
    })
}
export function JSlist1(data){
    return request({
        url:"/points-apply/JSList1",
        method:"post",
        data:data
    })
}

export function YZlist0(data){
    return request({
        url:"/points-apply/YZList0",
        method:"post",
        data:data
    })
}
export function GZRlist0(data){
    return request({
        url:"/points-apply/GZRList0",
        method:"post",
        data:data
    })
}
export function YZRlist0(data){
    return request({
        url:"/points-apply/YZRList0",
        method:"post",
        data:data
    })
}
export function DYlist0(data){
    return request({
        url:"/points-apply/DYList0",
        method:"post",
        data:data
    })
}
export function JSlist0(data){
    return request({
        url:"/points-apply/JSList0",
        method:"post",
        data:data
    })
}
export function banji(){
    return request({
        url:"/points-apply/class",
        method:"post"
    })
}
export function YZtg(id){
    return request({
        url:"/points-apply/YZtg?id="+id,
        method:"post"
    })
}
export function ZRtg(id){
    return request({
        url:"/points-apply/ZRtg?id="+id,
        method:"post"
    })
}
export function DYJStg(id){
    return request({
        url:"/points-apply/DYJStg?id="+id,
        method:"post"
    })
}
export function YZjj(id){
    return request({
        url:"/points-apply/YZjj?id="+id,
        method:"post"
    })
}
export function ZRjj(id){
    return request({
        url:"/points-apply/ZRjj?id="+id,
        method:"post"
    })
}
export function DYJSjj(id){
    return request({
        url:"/points-apply/DYJSjj?id="+id,
        method:"post"
    })
}
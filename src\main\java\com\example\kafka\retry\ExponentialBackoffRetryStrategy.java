package com.example.kafka.retry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Set;

/**
 * 指数退避重试策略
 */
public class ExponentialBackoffRetryStrategy implements RetryStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(ExponentialBackoffRetryStrategy.class);
    
    private final int maxRetries;
    private final long initialDelayMs;
    private final long maxDelayMs;
    private final double multiplier;
    private final long maxRetryTimeMs; // 最大重试时间窗口
    private final Set<Class<? extends Throwable>> retryableExceptions;
    private final Set<Class<? extends Throwable>> nonRetryableExceptions;
    
    public ExponentialBackoffRetryStrategy(int maxRetries, 
                                         long initialDelayMs, 
                                         long maxDelayMs,
                                         double multiplier,
                                         long maxRetryTimeMs,
                                         Set<Class<? extends Throwable>> retryableExceptions,
                                         Set<Class<? extends Throwable>> nonRetryableExceptions) {
        this.maxRetries = maxRetries;
        this.initialDelayMs = initialDelayMs;
        this.maxDelayMs = maxDelayMs;
        this.multiplier = multiplier;
        this.maxRetryTimeMs = maxRetryTimeMs;
        this.retryableExceptions = retryableExceptions;
        this.nonRetryableExceptions = nonRetryableExceptions;
    }
    
    @Override
    public <T> boolean shouldRetry(RetryableMessage<T> message, Throwable exception) {
        // 检查是否达到最大重试次数
        if (message.getRetryCount() >= maxRetries) {
            logger.warn("消息已达到最大重试次数: {}, 原始主题: {}", 
                       maxRetries, message.getOriginalTopic());
            return false;
        }
        
        // 检查是否超过最大重试时间窗口
        if (message.getFirstFailureTime() != null && maxRetryTimeMs > 0) {
            long elapsedTime = ChronoUnit.MILLIS.between(
                message.getFirstFailureTime(), LocalDateTime.now());
            if (elapsedTime > maxRetryTimeMs) {
                logger.warn("消息重试时间超过最大时间窗口: {}ms, 原始主题: {}", 
                           maxRetryTimeMs, message.getOriginalTopic());
                return false;
            }
        }
        
        // 检查异常类型
        if (isNonRetryableException(exception)) {
            logger.warn("遇到不可重试异常: {}, 原始主题: {}", 
                       exception.getClass().getSimpleName(), message.getOriginalTopic());
            return false;
        }
        
        if (retryableExceptions != null && !retryableExceptions.isEmpty()) {
            boolean isRetryable = retryableExceptions.stream()
                .anyMatch(clazz -> clazz.isAssignableFrom(exception.getClass()));
            if (!isRetryable) {
                logger.warn("异常类型不在可重试列表中: {}, 原始主题: {}", 
                           exception.getClass().getSimpleName(), message.getOriginalTopic());
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public <T> long calculateRetryDelay(RetryableMessage<T> message) {
        long delay = (long) (initialDelayMs * Math.pow(multiplier, message.getRetryCount()));
        return Math.min(delay, maxDelayMs);
    }
    
    @Override
    public String getRetryTopic(String originalTopic, int retryCount) {
        return originalTopic + ".retry." + retryCount;
    }
    
    @Override
    public String getDeadLetterTopic(String originalTopic) {
        return originalTopic + ".dlt";
    }
    
    /**
     * 检查是否为不可重试异常
     */
    private boolean isNonRetryableException(Throwable exception) {
        if (nonRetryableExceptions == null || nonRetryableExceptions.isEmpty()) {
            return false;
        }
        
        return nonRetryableExceptions.stream()
            .anyMatch(clazz -> clazz.isAssignableFrom(exception.getClass()));
    }
    
    /**
     * 建造者模式
     */
    public static class Builder {
        private int maxRetries = 3;
        private long initialDelayMs = 1000;
        private long maxDelayMs = 60000;
        private double multiplier = 2.0;
        private long maxRetryTimeMs = 0; // 0表示无限制
        private Set<Class<? extends Throwable>> retryableExceptions;
        private Set<Class<? extends Throwable>> nonRetryableExceptions;
        
        public Builder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        public Builder initialDelay(long initialDelayMs) {
            this.initialDelayMs = initialDelayMs;
            return this;
        }
        
        public Builder maxDelay(long maxDelayMs) {
            this.maxDelayMs = maxDelayMs;
            return this;
        }
        
        public Builder multiplier(double multiplier) {
            this.multiplier = multiplier;
            return this;
        }
        
        public Builder maxRetryTime(long maxRetryTimeMs) {
            this.maxRetryTimeMs = maxRetryTimeMs;
            return this;
        }
        
        public Builder retryableExceptions(Set<Class<? extends Throwable>> exceptions) {
            this.retryableExceptions = exceptions;
            return this;
        }
        
        public Builder nonRetryableExceptions(Set<Class<? extends Throwable>> exceptions) {
            this.nonRetryableExceptions = exceptions;
            return this;
        }
        
        public ExponentialBackoffRetryStrategy build() {
            return new ExponentialBackoffRetryStrategy(
                maxRetries, initialDelayMs, maxDelayMs, multiplier, 
                maxRetryTimeMs, retryableExceptions, nonRetryableExceptions);
        }
    }
}

<template>
  <div class="role-permission-container">
    <div class="page-header">
      <h2>角色权限</h2>
      <el-button type="primary" :icon="Refresh" circle @click="handleRefresh" />
    </div>

    <el-row :gutter="20">
      <!-- 角色列表 -->
      <el-col :span="6">
        <el-card shadow="hover" class="role-list-card">
          <template #header>
            <div class="card-header">
              <span>角色列表</span>
              <el-button type="primary" :icon="Plus" circle @click="handleAdd" />
            </div>
          </template>

          <div v-loading="loading" class="role-list">
            <div
              v-for="role in roleList"
              :key="role.roleId"
              class="role-item"
              :class="{ active: currentRole && currentRole.roleId === role.roleId }"
              @click="selectRole(role)"
            >
              <div class="role-name">{{ role.roleName }}</div>
              <div class="role-code">{{ role.roleKey }}</div>
              <div class="role-actions">
                <el-button
                  type="primary"
                  :icon="Edit"
                  circle
                  size="small"
                  @click.stop="handleEdit(role)"
                />
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  size="small"
                  @click.stop="handleDelete(role)"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 角色详情和权限 -->
      <el-col :span="18">
        <el-card shadow="hover" class="role-detail-card" v-if="currentRole">
          <template #header>
            <div class="card-header">
              <span>角色详情</span>
              <el-button type="primary" :icon="Edit" @click="handleEdit(currentRole)"
                >编辑</el-button
              >
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="角色名称">{{ currentRole.roleName }}</el-descriptions-item>
            <el-descriptions-item label="角色编码">{{ currentRole.roleKey }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{
              currentRole.createTime
            }}</el-descriptions-item>
            <el-descriptions-item label="用户数量">{{
              currentRole.userCount
            }}</el-descriptions-item>
            <el-descriptions-item label="角色描述" :span="2">{{
              currentRole.remark || '暂无描述'
            }}</el-descriptions-item>
          </el-descriptions>

          <div class="permission-header">
            <h3>权限列表</h3>
            <el-button type="primary" size="small" @click="handlePermissionEdit"
              >编辑权限</el-button
            >
          </div>

          <div class="permission-tree" v-loading="menuLoading">
            <el-tree
              :data="permissionTree"
              :default-expanded-keys="defaultExpandedKeys"
              :props="treeProps"
              node-key="menuId"
              :expand-on-click-node="false"
            >
              <template #default="{ node, data }">
                <div :class="getPermissionItemClass(data.menuId)">
                  <span>{{ data.menuName }}</span>
                  <el-tag v-if="hasPermission(data.menuId)" size="small" type="success"
                    >已授权</el-tag
                  >
                </div>
              </template>
            </el-tree>
          </div>
        </el-card>

        <el-empty v-else description="请选择角色" />
      </el-col>
    </el-row>

    <!-- 添加/编辑角色对话框 -->
    <el-dialog v-model="dialogVisible" :title="formTitle" width="600px" destroy-on-close>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" />
        </el-form-item>

        <el-form-item label="角色编码" prop="roleKey">
          <el-input v-model="form.roleKey" />
        </el-form-item>

        <el-form-item label="角色描述" prop="remark">
          <el-input v-model="form.remark" type="textarea" rows="3" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限编辑对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="编辑角色权限"
      width="600px"
      destroy-on-close
    >
      <div v-loading="menuLoading">
        <p class="permission-role-name">
          当前角色: <strong>{{ currentRole?.roleName }}</strong>
        </p>

        <el-tree
          ref="permissionTreeRef"
          :data="permissionTree"
          :default-expanded-keys="defaultExpandedKeys"
          show-checkbox
          :check-strictly="true"
          :props="treeProps"
          node-key="menuId"
          :default-checked-keys="permissionCheckedKeys"
          :expand-on-click-node="false"
        >
          <template #default="{ node, data }">
            <div class="permission-item">
              <span>{{ data.menuName }}</span>
            </div>
          </template>
        </el-tree>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermissionForm" :loading="submitPermissionLoading"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed, nextTick } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Edit, Delete, Refresh } from '@element-plus/icons-vue';
  import { findAllMenu } from '@/api/system/menu.js';
  import {
    findAllRole,
    findRoleById,
    addRole,
    updateRole,
    deleteRole,
    updateRolePermission
  } from '@/api/system/role.js';

  const loading = ref(false);
  const menuLoading = ref(false);
  const submitLoading = ref(false);
  const submitPermissionLoading = ref(false);
  const currentRole = ref(null);

  // 对话框控制
  const roleList = ref([]);
  const dialogVisible = ref(false);
  const permissionDialogVisible = ref(false);
  const dialogType = ref('add'); // 'add' 或 'edit'
  const formTitle = ref('添加角色');

  // 权限树引用
  const permissionTreeRef = ref(null);
  const permissionCheckedKeys = ref([]);

  // 表单数据
  const form = ref({
    roleId: '',
    roleName: '',
    roleKey: '',
    remark: ''
  });

  // 表单规则
  const rules = {
    roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    roleKey: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
  };

  const formRef = ref(null);

  // 权限树数据
  const permissionTree = ref([]);

  // 默认展开的节点
  const defaultExpandedKeys = ref([]);
  //配置el-tree的props，让组件能识别后端返回数据的字段
  const treeProps = {
    children: 'children', //指定子节点数组的字段名
    label: 'menuName' //指定显示文本的字段名
  };

  // 获取菜单列表
  const fetchMenus = async () => {
    menuLoading.value = true;
    try {
      const res = await findAllMenu();
      if (res.data.code === 200) {
        // 将后端返回的数据赋值给 permissionTree
        permissionTree.value = res.data.data;

        // 提取顶级菜单ID作为默认展开的节点
        if (permissionTree.value && permissionTree.value.length > 0) {
          defaultExpandedKeys.value = permissionTree.value
            .filter((item) => item.menuId)
            .map((item) => item.menuId.toString());
        }
      } else {
        ElMessage.error(res.data.message || '获取菜单列表失败');
      }
    } catch (error) {
      console.error('获取菜单列表失败:', error);
      ElMessage.error('获取菜单列表失败，请稍后重试');
    } finally {
      menuLoading.value = false;
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    loading.value = true;
    try {
      const res = await findAllRole();
      if (res.data.code === 200) {
        roleList.value = res.data.data;

        // 默认选中第一个角色
        if (roleList.value && roleList.value.length > 0) {
          await selectRole(roleList.value[0]);
        } else {
          currentRole.value = null;
        }
      } else {
        ElMessage.error(res.data.message || '获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      ElMessage.error('获取角色列表失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  // 生命周期钩子
  onMounted(async () => {
    await fetchMenus();
    await fetchRoles();
  });

  // 选中角色
  const selectRole = async (role) => {
    try {
      // 查询角色详情，获取最新数据
      const detailRes = await findRoleById(role.roleId);

      if (detailRes && detailRes.data) {
        currentRole.value = detailRes.data;

        // 提取角色权限ID
        if (currentRole.value.permissionIds) {
          // 将权限ID数组转换为menuId数组
          const menuIds = currentRole.value.permissionIds
            .filter((item) => item && item.menuId)
            .map((item) => item.menuId);

          currentRole.value.menuIds = menuIds;
        } else {
          currentRole.value.menuIds = [];
        }
      } else {
        currentRole.value = role;
        currentRole.value.menuIds = [];
      }
    } catch (error) {
      console.error('获取角色详情失败:', error);
      currentRole.value = role;
      currentRole.value.menuIds = [];
    }
  };

  // 添加角色
  const handleAdd = () => {
    dialogType.value = 'add';
    formTitle.value = '添加角色';
    resetForm();
    dialogVisible.value = true;
  };

  // 编辑角色
  const handleEdit = (role) => {
    dialogType.value = 'edit';
    formTitle.value = '编辑角色';
    resetForm();

    // 填充表单数据
    form.value.roleId = role.roleId;
    form.value.roleName = role.roleName;
    form.value.roleKey = role.roleKey;
    form.value.remark = role.remark || '';

    dialogVisible.value = true;
  };

  // 删除角色
  const handleDelete = (role) => {
    ElMessageBox.confirm(`确定要删除角色 "${role.roleName}" 吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await deleteRole(role.roleId);

          if (res.data.code === 200) {
            ElMessage.success('删除角色成功');

            // 删除成功后重新加载角色列表
            await fetchRoles();

            // 如果当前选中的是被删除的角色，清空当前角色
            if (currentRole.value && currentRole.value.roleId === role.roleId) {
              currentRole.value = roleList.value.length > 0 ? roleList.value[0] : null;
            }
          } else {
            ElMessage.error(res.data.message || '删除角色失败');
          }
        } catch (error) {
          console.error('删除角色失败:', error);
          ElMessage.error('删除角色失败，请稍后重试');
        }
      })
      .catch(() => {});
  };

  // 辅助函数：递归获取所有菜单ID
  function getAllMenuIds(tree) {
    let ids = [];
    tree.forEach((item) => {
      ids.push(item.menuId);
      if (item.children && item.children.length > 0) {
        ids = ids.concat(getAllMenuIds(item.children));
      }
    });
    return ids;
  }

  // 编辑权限
  const handlePermissionEdit = () => {
    if (!currentRole.value) {
      ElMessage.warning('请先选择角色');
      return;
    }

    // 获取当前菜单树所有 menuId
    const validMenuIds = getAllMenuIds(permissionTree.value);

    // 只保留当前菜单树中存在的 menuId
    permissionCheckedKeys.value = (currentRole.value.menuIds || []).filter((id) =>
      validMenuIds.includes(id)
    );

    // 打开权限编辑对话框
    permissionDialogVisible.value = true;

    // 等待DOM更新后设置默认选中的权限
    nextTick(() => {
      if (permissionTreeRef.value) {
        permissionTreeRef.value.setCheckedKeys(permissionCheckedKeys.value);
      }
    });
  };

  // 提交表单（添加/编辑角色）
  const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
      if (valid) {
        submitLoading.value = true;

        try {
          let res;

          if (dialogType.value === 'add') {
            // 添加角色
            res = await addRole(form.value);
          } else {
            // 编辑角色
            res = await updateRole(form.value);
          }

          if (res.data.code === 200) {
            ElMessage.success(dialogType.value === 'add' ? '添加角色成功' : '编辑角色成功');

            // 关闭对话框
            dialogVisible.value = false;

            // 重新加载角色列表
            await fetchRoles();

            // 如果是编辑当前选中的角色，需要重新选择该角色以更新详情
            if (
              dialogType.value === 'edit' &&
              currentRole.value &&
              currentRole.value.roleId === form.value.roleId
            ) {
              const updatedRole = roleList.value.find((role) => role.roleId === form.value.roleId);
              if (updatedRole) {
                await selectRole(updatedRole);
              }
            }
          } else {
            ElMessage.error(
              res.data.message || (dialogType.value === 'add' ? '添加角色失败' : '编辑角色失败')
            );
          }
        } catch (error) {
          console.error(dialogType.value === 'add' ? '添加角色失败:' : '编辑角色失败:', error);
          ElMessage.error(
            dialogType.value === 'add' ? '添加角色失败，请稍后重试' : '编辑角色失败，请稍后重试'
          );
        } finally {
          submitLoading.value = false;
        }
      }
    });
  };

  // 提交权限表单
  const submitPermissionForm = async () => {
    if (!currentRole.value) {
      ElMessage.warning('请先选择角色');
      return;
    }

    if (!permissionTreeRef.value) {
      ElMessage.warning('权限树加载失败，请刷新重试');
      return;
    }

    submitPermissionLoading.value = true;

    try {
      // 获取选中的权限ID
      const checkedKeys = permissionTreeRef.value.getCheckedKeys();
      const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys();

      // 合并完全选中和半选中的节点
      const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys];

      // 调用API更新角色权限
      const res = await updateRolePermission(currentRole.value.roleId, allCheckedKeys);

      if (res.data.code === 200) {
        ElMessage.success('更新角色权限成功');

        // 关闭权限编辑对话框
        permissionDialogVisible.value = false;

        // 更新当前角色的权限
        currentRole.value.menuIds = allCheckedKeys;

        // 重新获取角色列表
        await fetchRoles();

        // 重新选择当前角色以更新详情
        const updatedRole = roleList.value.find((role) => role.roleId === currentRole.value.roleId);
        if (updatedRole) {
          await selectRole(updatedRole);
        }
      } else {
        ElMessage.error(res.data.message || '更新角色权限失败');
      }
    } catch (error) {
      console.error('更新角色权限失败:', error);
      ElMessage.error('更新角色权限失败，请稍后重试');
    } finally {
      submitPermissionLoading.value = false;
    }
  };

  // 重置表单
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields();
    }

    form.value.roleId = '';
    form.value.roleName = '';
    form.value.roleKey = '';
    form.value.remark = '';
  };

  // 刷新数据
  const handleRefresh = async () => {
    await fetchMenus();
    await fetchRoles();
  };

  // 检查权限是否被选中
  const hasPermission = (menuId) => {
    if (
      !currentRole.value ||
      !currentRole.value.menuIds ||
      !Array.isArray(currentRole.value.menuIds)
    ) {
      return false;
    }

    return currentRole.value.menuIds.includes(menuId);
  };

  // 获取权限项的样式
  const getPermissionItemClass = (menuId) => {
    return {
      'permission-item': true,
      'permission-item-active': hasPermission(menuId)
    };
  };
</script>

<style scoped>
  .role-permission-container {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    display: flex;
    flex-direction: column;
    min-height: 100%;
  }

  /* 隐藏WebKit浏览器的滚动条 */
  .role-permission-container::-webkit-scrollbar {
    display: none;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .page-header h2 {
    margin: 0;
    font-size: 22px;
    color: #303133;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .role-list-card,
  .role-detail-card {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: visible;
    transition: all 0.3s;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s ease-in-out;
    height: calc(100vh - 180px);
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .role-list-card::-webkit-scrollbar,
  .role-detail-card::-webkit-scrollbar {
    display: none;
  }

  .role-list-card:hover,
  .role-detail-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }

  .role-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .role-item {
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
  }

  .role-item:hover {
    background-color: #f5f7fa;
  }

  .role-item.active {
    background-color: #ecf5ff;
    border-color: #409eff;
  }

  .role-name {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .role-code {
    font-size: 12px;
    color: #909399;
  }

  .role-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;
  }

  .role-item:hover .role-actions {
    display: flex;
    gap: 5px;
  }

  .permission-header {
    margin-top: 20px;
    margin-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .permission-header h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }

  .permission-tree {
    margin-top: 10px;
  }

  .permission-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
  }

  .permission-item-active {
    color: #409eff;
    font-weight: bold;
  }

  .permission-role-name {
    margin-bottom: 15px;
    font-size: 14px;
    color: #606266;
  }

  /* 添加动画效果 */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式调整 */
  @media screen and (max-width: 768px) {
    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }
</style>

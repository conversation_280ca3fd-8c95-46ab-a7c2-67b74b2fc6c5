package com.zhentao.file;


import com.alibaba.fastjson.JSON;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.SysUser;
import com.zhentao.utils.JwtService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class AuthFile implements Filter {
    // 不需要验证的路径
    private static final List<String> WHITELIST_PATHS = Arrays.asList(
            "/user/login",
            "/user/logout",
            "/error",
            "/user/register",
            "/user/captcha",
            "/edu-student/getClassOptions",
            "/api/minio/uploadFile",
            "/points-record/findAll",
            "/activity/findActivity",
            "/activityApplication/signUpActivity",
            "/edu-student/template/download",
            "/edu-student/batch/import",
            "/edu-student/batch/validate",
            "/edu-student/queryTopTenStudents",
            "/edu-student/queryBottomTenStudents",
            "/edu-class/queryTopThreeClass",
            "/points-apply/queryTodayAddPoints",
            "/points-apply/queryTodayMinusPoints",
            "/points-apply/studentList",
            "/activityApplication/findStudentActivity",
            "/user/register"
    );

    // 需要刷新token的路径（避免频繁刷新）
    private static final List<String> REFRESH_TOKEN_PATHS = Arrays.asList(
            "/dashboard",
            "/student/list",
            "/points/history",
            "/edu-student/findPage"
    );

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        HttpServletRequest request = (HttpServletRequest) servletRequest;

        String url = request.getRequestURI();
        // 允许白名单路径不需要JWT验证
        if (url.startsWith("http://localhost:8080/minio/uploadFile") || WHITELIST_PATHS.contains(url)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        String jwt = request.getHeader("Authorization");
        // 处理前端可能添加的Bearer前缀
        if (jwt != null && jwt.startsWith("Bearer ")) {
            jwt = jwt.substring(7);
        }

        if (jwt != null && jwt.length() > 0) {
            int verifyResult = JwtService.verifyToken(jwt);
            if (verifyResult == 1) {
                // JWT有效，继续处理
                processValidToken(jwt, request, response, filterChain);
                return;
            } else if (verifyResult == -2) {
                // Token过期，尝试刷新
                String refreshedToken = JwtService.refreshToken(jwt);
                if (refreshedToken != null) {
                    // 设置新的token到响应头
                    response.setHeader("New-Token", refreshedToken);
                    response.setHeader("Access-Control-Expose-Headers", "New-Token");

                    // 使用刷新的token继续处理
                    processValidToken(refreshedToken, request, response, filterChain);
                    return;
                } else {
                    sendErrorResponse(response, "JWT令牌已过期且无法刷新，请重新登录", 401);
                    return;
                }
            } else {
                // JWT无效，返回不同的错误信息
                String errorMessage;
                switch (verifyResult) {
                    case -1:
                        errorMessage = "无效的JWT令牌";
                        break;
                    case -3:
                        errorMessage = "JWT令牌不存在";
                        break;
                    default:
                        errorMessage = "JWT验证失败，请重新登录";
                }
                sendErrorResponse(response, errorMessage, 401);
                return;
            }
        }

        // 没有提供JWT，返回错误
        sendErrorResponse(response, "请先登录", 401);
    }

    /**
     * 处理有效的Token
     */
    private void processValidToken(String jwt, HttpServletRequest request,
                                   HttpServletResponse response, FilterChain filterChain)
            throws IOException, ServletException {
        Map<String, Object> map = JwtService.getClaimsMap(jwt);
        if (map != null) {
            Object username = map.get("username");
            Object userId = map.get("userId");
            Object perms = map.get("perms");
            Object student = map.get("student");

            SysUser user = new SysUser();
            user.setPerms((List<String>) perms);
            user.setUserId((Integer) userId);
            user.setUsername((String) username);
            user.setEduStudent((EduStudent) student);
            UserContext.setUserThreadLocal(user);

            UsernamePasswordAuthenticationToken token =
                    new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
            token.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(token);

            // 检查是否需要刷新token（对于某些关键路径）
            String url = request.getRequestURI();
            if (REFRESH_TOKEN_PATHS.contains(url)) {
                String refreshedToken = JwtService.refreshToken(jwt);
                if (refreshedToken != null) {
                    response.setHeader("New-Token", refreshedToken);
                    response.setHeader("Access-Control-Expose-Headers", "New-Token");
                }
            }

            filterChain.doFilter(request, response);
        } else {
            sendErrorResponse(response, "无效的JWT令牌内容", 401);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpServletResponse response, String message, int statusCode) throws IOException {
        Result result = Result.ERROR(message);
        response.setStatus(statusCode);
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }
}

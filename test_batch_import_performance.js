/**
 * 批量导入性能测试脚本
 * 用于测试优化后的批量导入功能性能
 */

// 生成测试数据
const generateTestData = (studentCount = 100, classCount = 5) => {
  const classes = [];
  const studentsPerClass = Math.ceil(studentCount / classCount);
  
  for (let i = 1; i <= classCount; i++) {
    const students = [];
    
    for (let j = 1; j <= studentsPerClass && students.length < studentCount; j++) {
      const studentIndex = (i - 1) * studentsPerClass + j;
      if (studentIndex > studentCount) break;
      
      students.push({
        studentNo: `2024${String(studentIndex).padStart(6, '0')}`,
        realName: `测试学生${studentIndex}`
      });
    }
    
    classes.push({
      className: `测试班级${i}`,
      counselor: `辅导员${i}`,
      teacher: `讲师${i}`,
      classroom: `教室${i}01`,
      stageName: `测试阶段${i}`,
      courseName: `测试课程${i}`,
      students: students
    });
  }
  
  return { classes };
};

// 性能测试函数
const testBatchImportPerformance = async (studentCount = 100) => {
  console.log(`开始批量导入性能测试，学生数量: ${studentCount}`);
  
  const startTime = Date.now();
  
  try {
    // 生成测试数据
    const testData = generateTestData(studentCount);
    console.log(`生成测试数据完成，班级数: ${testData.classes.length}`);
    
    // 执行批量导入
    const response = await fetch('/edu-student/batch/import', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('Authorization')
      },
      body: JSON.stringify(testData)
    });
    
    const result = await response.json();
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('批量导入结果:', result);
    console.log(`批量导入耗时: ${duration}ms (${(duration/1000).toFixed(2)}秒)`);
    
    // 性能评估
    const studentsPerSecond = (studentCount / (duration / 1000)).toFixed(2);
    console.log(`处理速度: ${studentsPerSecond} 学生/秒`);
    
    // 性能等级评估
    if (duration < 5000) { // 小于5秒
      console.log('✅ 性能优秀 - 批量导入在5秒内完成');
    } else if (duration < 15000) { // 小于15秒
      console.log('⚠️ 性能良好 - 批量导入在15秒内完成');
    } else if (duration < 60000) { // 小于1分钟
      console.log('🔶 性能一般 - 批量导入在1分钟内完成');
    } else {
      console.log('❌ 性能需要改进 - 批量导入超过1分钟');
    }
    
    return {
      success: result.code === 200,
      duration: duration,
      studentCount: studentCount,
      studentsPerSecond: parseFloat(studentsPerSecond),
      result: result
    };
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.error('批量导入失败:', error);
    console.log(`失败前耗时: ${duration}ms`);
    
    return {
      success: false,
      duration: duration,
      studentCount: studentCount,
      error: error.message
    };
  }
};

// 批量性能测试
const runBatchPerformanceTests = async () => {
  console.log('开始批量导入性能测试套件...\n');
  
  const testCases = [
    { name: '小规模测试', studentCount: 50 },
    { name: '中规模测试', studentCount: 200 },
    { name: '大规模测试', studentCount: 500 },
    { name: '超大规模测试', studentCount: 1000 }
  ];
  
  const results = [];
  
  for (const testCase of testCases) {
    console.log(`\n--- ${testCase.name} (${testCase.studentCount}条数据) ---`);
    
    const result = await testBatchImportPerformance(testCase.studentCount);
    results.push({
      ...result,
      testName: testCase.name
    });
    
    // 测试间隔，避免对系统造成过大压力
    if (testCase !== testCases[testCases.length - 1]) {
      console.log('等待10秒后进行下一次测试...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
  }
  
  // 生成性能报告
  generatePerformanceReport(results);
  
  return results;
};

// 生成性能报告
const generatePerformanceReport = (results) => {
  console.log('\n=== 批量导入性能测试报告 ===');
  
  const successResults = results.filter(r => r.success);
  const failResults = results.filter(r => !r.success);
  
  console.log(`总测试数: ${results.length}`);
  console.log(`成功测试: ${successResults.length}`);
  console.log(`失败测试: ${failResults.length}`);
  
  if (successResults.length > 0) {
    console.log('\n--- 成功测试详情 ---');
    successResults.forEach(result => {
      console.log(`${result.testName}:`);
      console.log(`  - 数据量: ${result.studentCount} 条`);
      console.log(`  - 耗时: ${(result.duration/1000).toFixed(2)} 秒`);
      console.log(`  - 速度: ${result.studentsPerSecond} 学生/秒`);
      console.log(`  - 成功率: ${((result.result.data?.successCount || 0) / result.studentCount * 100).toFixed(1)}%`);
    });
    
    // 计算平均性能
    const avgDuration = successResults.reduce((sum, r) => sum + r.duration, 0) / successResults.length;
    const avgSpeed = successResults.reduce((sum, r) => sum + r.studentsPerSecond, 0) / successResults.length;
    
    console.log('\n--- 平均性能指标 ---');
    console.log(`平均耗时: ${(avgDuration/1000).toFixed(2)} 秒`);
    console.log(`平均速度: ${avgSpeed.toFixed(2)} 学生/秒`);
    
    // 性能趋势分析
    console.log('\n--- 性能趋势分析 ---');
    const sortedResults = successResults.sort((a, b) => a.studentCount - b.studentCount);
    for (let i = 1; i < sortedResults.length; i++) {
      const prev = sortedResults[i-1];
      const curr = sortedResults[i];
      const dataRatio = curr.studentCount / prev.studentCount;
      const timeRatio = curr.duration / prev.duration;
      const efficiency = dataRatio / timeRatio;
      
      console.log(`${prev.testName} -> ${curr.testName}:`);
      console.log(`  数据量增长: ${dataRatio.toFixed(2)}倍, 时间增长: ${timeRatio.toFixed(2)}倍`);
      console.log(`  效率比: ${efficiency.toFixed(2)} ${efficiency > 0.9 ? '✅' : efficiency > 0.7 ? '⚠️' : '❌'}`);
    }
  }
  
  if (failResults.length > 0) {
    console.log('\n--- 失败测试详情 ---');
    failResults.forEach(result => {
      console.log(`${result.testName}: ${result.error}`);
    });
  }
};

// 内存使用监控
const monitorMemoryUsage = () => {
  if (performance.memory) {
    const memory = performance.memory;
    console.log('\n=== 内存使用情况 ===');
    console.log(`已使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`总分配: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
    console.log(`使用率: ${(memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)}%`);
  } else {
    console.log('浏览器不支持内存监控');
  }
};

// 网络性能测试
const testNetworkPerformance = async () => {
  console.log('\n=== 网络性能测试 ===');
  
  const startTime = performance.now();
  
  try {
    const response = await fetch('/edu-student/batch/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('Authorization')
      },
      body: JSON.stringify(generateTestData(10, 1))
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`网络延迟: ${duration.toFixed(2)}ms`);
    
    if (duration < 200) {
      console.log('✅ 网络性能优秀');
    } else if (duration < 1000) {
      console.log('⚠️ 网络性能良好');
    } else {
      console.log('❌ 网络性能较差，可能影响批量导入');
    }
    
    return duration;
    
  } catch (error) {
    console.error('网络测试失败:', error);
    return -1;
  }
};

// 导出测试函数供控制台使用
if (typeof window !== 'undefined') {
  window.testBatchImportPerformance = testBatchImportPerformance;
  window.runBatchPerformanceTests = runBatchPerformanceTests;
  window.generateTestData = generateTestData;
  window.monitorMemoryUsage = monitorMemoryUsage;
  window.testNetworkPerformance = testNetworkPerformance;
  
  console.log('批量导入性能测试工具已加载！');
  console.log('使用方法:');
  console.log('- testBatchImportPerformance(100): 单次导入测试');
  console.log('- runBatchPerformanceTests(): 完整性能测试套件');
  console.log('- generateTestData(100, 5): 生成测试数据');
  console.log('- monitorMemoryUsage(): 监控内存使用');
  console.log('- testNetworkPerformance(): 测试网络性能');
}

// Node.js 环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testBatchImportPerformance,
    runBatchPerformanceTests,
    generateTestData,
    monitorMemoryUsage,
    testNetworkPerformance
  };
}

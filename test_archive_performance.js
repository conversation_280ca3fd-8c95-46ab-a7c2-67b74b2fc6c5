/**
 * 归档功能性能测试脚本
 * 用于测试优化后的归档功能性能
 */

// 模拟前端测试
const testArchivePerformance = async () => {
  console.log('开始归档性能测试...');
  
  const startTime = Date.now();
  
  try {
    // 模拟API调用
    const response = await fetch('/edu-student/archive', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('Authorization')
      }
    });
    
    const result = await response.json();
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('归档操作结果:', result);
    console.log(`归档操作耗时: ${duration}ms (${(duration/1000).toFixed(2)}秒)`);
    
    if (duration < 10000) { // 小于10秒
      console.log('✅ 性能优秀 - 归档操作在10秒内完成');
    } else if (duration < 30000) { // 小于30秒
      console.log('⚠️ 性能良好 - 归档操作在30秒内完成');
    } else {
      console.log('❌ 性能需要改进 - 归档操作超过30秒');
    }
    
    return {
      success: result.message === '添加成功',
      duration: duration,
      result: result
    };
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.error('归档操作失败:', error);
    console.log(`失败前耗时: ${duration}ms`);
    
    return {
      success: false,
      duration: duration,
      error: error.message
    };
  }
};

// 批量测试函数
const runPerformanceTests = async (testCount = 3) => {
  console.log(`开始执行 ${testCount} 次归档性能测试...`);
  
  const results = [];
  
  for (let i = 1; i <= testCount; i++) {
    console.log(`\n--- 第 ${i} 次测试 ---`);
    const result = await testArchivePerformance();
    results.push(result);
    
    // 测试间隔
    if (i < testCount) {
      console.log('等待5秒后进行下一次测试...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  // 统计结果
  const successCount = results.filter(r => r.success).length;
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  const minDuration = Math.min(...results.map(r => r.duration));
  const maxDuration = Math.max(...results.map(r => r.duration));
  
  console.log('\n=== 测试结果统计 ===');
  console.log(`成功率: ${successCount}/${testCount} (${(successCount/testCount*100).toFixed(1)}%)`);
  console.log(`平均耗时: ${(avgDuration/1000).toFixed(2)}秒`);
  console.log(`最短耗时: ${(minDuration/1000).toFixed(2)}秒`);
  console.log(`最长耗时: ${(maxDuration/1000).toFixed(2)}秒`);
  
  return {
    successRate: successCount / testCount,
    avgDuration: avgDuration,
    minDuration: minDuration,
    maxDuration: maxDuration,
    results: results
  };
};

// 内存使用监控
const monitorMemoryUsage = () => {
  if (performance.memory) {
    const memory = performance.memory;
    console.log('内存使用情况:');
    console.log(`- 已使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`- 总分配: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`- 限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
  }
};

// 网络性能测试
const testNetworkPerformance = async () => {
  console.log('测试网络连接性能...');
  
  const startTime = performance.now();
  
  try {
    const response = await fetch('/edu-student/getClassOptions', {
      method: 'GET',
      headers: {
        'Authorization': localStorage.getItem('Authorization')
      }
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`网络延迟: ${duration.toFixed(2)}ms`);
    
    if (duration < 100) {
      console.log('✅ 网络性能优秀');
    } else if (duration < 500) {
      console.log('⚠️ 网络性能良好');
    } else {
      console.log('❌ 网络性能较差，可能影响归档操作');
    }
    
    return duration;
    
  } catch (error) {
    console.error('网络测试失败:', error);
    return -1;
  }
};

// 导出测试函数供控制台使用
if (typeof window !== 'undefined') {
  window.testArchivePerformance = testArchivePerformance;
  window.runPerformanceTests = runPerformanceTests;
  window.monitorMemoryUsage = monitorMemoryUsage;
  window.testNetworkPerformance = testNetworkPerformance;
  
  console.log('归档性能测试工具已加载！');
  console.log('使用方法:');
  console.log('- testArchivePerformance(): 单次归档测试');
  console.log('- runPerformanceTests(3): 批量归档测试');
  console.log('- monitorMemoryUsage(): 监控内存使用');
  console.log('- testNetworkPerformance(): 测试网络性能');
}

// Node.js 环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testArchivePerformance,
    runPerformanceTests,
    monitorMemoryUsage,
    testNetworkPerformance
  };
}

package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.dto.system.system.PasswordDto;
import com.zhentao.dto.system.system.SysUserDto;
import com.zhentao.mapper.*;
import com.zhentao.pojo.*;
import com.zhentao.service.SysUserService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService , UserDetailsService {
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private EduStudentMapper sysStudentMapper;
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        QueryWrapper<SysUser> sysUserQueryWrapper=new QueryWrapper<>();

        SysUser sysUser = null;
        // 判断username是用户名还是手机号
        if (username.matches("^1[3-9]\\d{8}$")) {
            sysUserQueryWrapper.eq("phone",username);
            sysUser = sysUserMapper.selectOne(sysUserQueryWrapper);
        }else{
            sysUserQueryWrapper.eq("username",username);
            sysUser = sysUserMapper.selectOne(sysUserQueryWrapper);
        }
        
        if (sysUser == null) {
            // 当用户不存在时，抛出UsernameNotFoundException异常而不是返回null
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        Integer userId = sysUser.getUserId();
        QueryWrapper<SysUserRole> sysUserRoleQueryWrapper=new QueryWrapper<>();
        sysUserRoleQueryWrapper.eq("user_id",userId);
        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(sysUserRoleQueryWrapper);
        if (sysUserRoles!=null){
            List<Integer> roleIds=new ArrayList<>();
            for (SysUserRole sysUserRole : sysUserRoles) {
                roleIds.add(sysUserRole.getRoleId());
            }
                QueryWrapper<SysRoleMenu> sysRoleMenuQueryWrapper=new QueryWrapper<>();
                sysRoleMenuQueryWrapper.in("role_id",roleIds);
                sysRoleMenuQueryWrapper.select("distinct menu_id");
                List<Integer> menuId = sysRoleMenuMapper.selectObjs(sysRoleMenuQueryWrapper);

                List<SysMenu> sysMenus = sysMenuMapper.selectBatchIds(menuId);
                List<String> perms = new ArrayList<>();
                for (SysMenu sysMenu : sysMenus) {
                    perms.add(sysMenu.getPerms());
                }
                sysUser.setPerms(perms);
                sysUser.setLoginDate(new Date());
                sysUserMapper.updateById(sysUser);

            return sysUser;
        }
        // 即使用户没有角色也应该返回用户对象，而不是null
        return sysUser;
    }

    @Override
    public Page<SysUser> getUserPage(SysUserDto sysUserDto) {
        Page<SysUser> page=new Page<>(sysUserDto.getPageCurrent(),sysUserDto.getPageSize());
        QueryWrapper<SysUser> sysUserQueryWrapper=new QueryWrapper<>();
        sysUserQueryWrapper.like(sysUserDto.getUsername()!=null && sysUserDto.getUsername()!="","username",sysUserDto.getUsername());
        sysUserQueryWrapper.like(sysUserDto.getRealName()!=null && sysUserDto.getRealName()!="","real_name",sysUserDto.getRealName());
        sysUserQueryWrapper.eq("del_flag",0);
        QueryWrapper<SysUserRole> sysUserRoleQueryWrapper=new QueryWrapper<>();
        //添加角色id条件
        sysUserRoleQueryWrapper.eq("role_id",sysUserDto.getRoleId());
        //去重
        sysUserRoleQueryWrapper.select("distinct user_id");
        List<Integer> userIds = sysUserRoleMapper.selectObjs(sysUserRoleQueryWrapper);
        //添加用户id条件
        sysUserQueryWrapper.in(userIds!=null && userIds.size()>0,"user_id",userIds);
        sysUserQueryWrapper.eq(sysUserDto.getStatus()!=null,"status",sysUserDto.getStatus());
        Page<SysUser> page1 = sysUserMapper.selectPage(page, sysUserQueryWrapper);
        List<SysUser> records = page1.getRecords();
        for (SysUser sysUser : records) {
            Integer userId = sysUser.getUserId();
            QueryWrapper<SysUserRole> sysUserRoleQueryWrapper1=new QueryWrapper<>();
            sysUserRoleQueryWrapper1.eq("user_id",userId);
            List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(sysUserRoleQueryWrapper1);
            List<Integer> roleIds=new ArrayList<>();
            List<String> roleNameList=new ArrayList<>();
            for (SysUserRole sysUserRole : sysUserRoles) {
                Integer roleId = sysUserRole.getRoleId();
                roleIds.add(roleId);
                //根据角色id查询角色信息
                SysRole sysRole = sysRoleMapper.selectById(roleId);
                if (sysRole!=null){
                    roleNameList.add(sysRole.getRoleName());
                }
            }
//           // 设置角色 ID 列表（如果需要单个角色 ID，可根据业务取第一个等，这里按多角色存列表）
            sysUser.setRoleIds(roleIds);
            sysUser.setRoleNames(Collections.singletonList(String.join(",", roleNameList)));
        }
        return page1;
    }

    @Override
    public Result addUserRole(SysUser sysUser) {
        if (sysUser!=null){
            String username = sysUser.getUsername();
            QueryWrapper<SysUser> sysUserQueryWrapper=new QueryWrapper<>();
            sysUserQueryWrapper.eq("username",username);
            SysUser sysUser1 = sysUserMapper.selectOne(sysUserQueryWrapper);
            if (sysUser1!=null){
                return Result.ERROR("用户名已存在");
            }
            String realName = sysUser.getRealName();
            QueryWrapper<SysUser> sysUserQueryWrapper1=new QueryWrapper<>();
            sysUserQueryWrapper1.eq("real_name",realName);
            SysUser sysUser2 = sysUserMapper.selectOne(sysUserQueryWrapper1);
            if (sysUser2!=null){
                return Result.ERROR("真实姓名已存在");
            }

            BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
            sysUser.setPassword(bCryptPasswordEncoder.encode("user123"));
            Integer userId1 = UserContext.getCurrentUser().getUserId();
            System.err.println(userId1);
            //创建人id
            sysUser.setCreateBy(userId1);
            sysUser.setCreateTime(new Date());
            sysUser.setStatus(0);
            sysUser.setAvatar("http://182.254.244.209:9000/jifen/f9256155491e54bf5e99bf29eece0156_512_512.jpg");
            sysUserMapper.insert(sysUser);
            Integer userId = sysUser.getUserId();
            System.err.println(userId);
            QueryWrapper<SysUserRole> sysUserRoleQueryWrapper=new QueryWrapper<>();
            sysUserRoleQueryWrapper.eq("user_id",userId);
            SysUserRole sysUserRole = sysUserRoleMapper.selectOne(sysUserRoleQueryWrapper);
            if (sysUserRole!=null){
                sysUserRoleMapper.delete(sysUserRoleQueryWrapper);
            }else {
                List<Integer> roleIds = sysUser.getRoleIds();
                for (Integer roleId : roleIds) {
                    sysUserRole=new SysUserRole();
                    sysUserRole.setUserId(userId);
                    sysUserRole.setRoleId(roleId);
                    sysUserRoleMapper.insert(sysUserRole);
                }
            }
            return Result.OK("添加成功");
        }else {
            return Result.ERROR("添加失败");
        }
    }

    @Override
    public Result updateUserRole(SysUser sysUser) {
        //1. 控制校验
        if (sysUser == null || sysUser.getUserId() == null){
            return Result.ERROR("用户信息为空，修改失败");
        }
        //2. 填充更新人，更新时间（如果需要）
        sysUser.setUpdateBy(UserContext.getCurrentUser().getUserId());
        sysUser.setUpdateTime(new Date());
        //更新用户基础信息（如果需要,可根据实际业务决定是否保留）
        sysUserMapper.updateById(sysUser);
        //3. 处理角色关系，先删除就关系，再查新关系
        Integer userId = sysUser.getUserId();
        List<Integer> newRoleIds = sysUser.getRoleIds();
        //3.1 删除用户原有角色关系
        QueryWrapper<SysUserRole> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("user_id", userId);
        sysUserRoleMapper.delete(deleteWrapper);
        //3.2 批量添加用户新角色关系
        if (newRoleIds != null && !newRoleIds.isEmpty()) {
            List<SysUserRole> userRoleList = new ArrayList<>();
            for (Integer roleId : newRoleIds) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRoleList.add(userRole);
            }
            //3.3 批量插入（需确保SysUserRoleMapper支持批量插入）
            sysUserRoleMapper.insert(userRoleList);
            return Result.OK("更新成功");
        }else {
            return Result.OK("更新失败");
        }
    }

    @Override
    public Result deleteUserRole(Integer userId) {
        System.err.println(userId);
        if (userId!=null){
            SysUser sysUser = sysUserMapper.selectById(userId);

            sysUser.setDelFlag(1);
            sysUser.setUpdateBy(UserContext.getCurrentUser().getUserId());
            sysUser.setUpdateTime(new Date());
            sysUserMapper.updateById(sysUser);
            return Result.OK("删除成功");
        }else{
            return Result.ERROR("删除失败");
        }

    }

    @Override
    public Result updateUserStatus(Integer userId, Integer status) {
        if (userId!=null){
            SysUser sysUser = sysUserMapper.selectById(userId);
            sysUser.setUserId(userId);
            sysUser.setStatus(status);
            sysUserMapper.updateById(sysUser);
            return Result.OK("修改成功");
        }
        return null;
    }

    @Override
    public Result resetUserPassword(Integer userId) {
        if (userId!=null){
            SysUser sysUser = sysUserMapper.selectById(userId);
            BCryptPasswordEncoder bCryptPasswordEncoder=new BCryptPasswordEncoder();
            String user = bCryptPasswordEncoder.encode("user");
            sysUser.setPassword(user);
            int i = sysUserMapper.updateById(sysUser);
            if (i>0){
                return Result.OK("重置成功");
            }else {
                return Result.ERROR("重置失败");
            }
        }else {
            return Result.ERROR("用户不存在");
        }
    }

    /**
     * 修改个人信息
     *
     * @param sysUser
     */
    @Override
    public Result updateUserInfo(SysUser sysUser) {
        if (sysUser!=null){
            Integer userId = UserContext.getCurrentUser().getUserId();
            SysUser sysUser1 = sysUserMapper.selectById(userId);
            sysUser1.setUserId(userId);
            sysUser1.setRealName(sysUser.getRealName());
            sysUser1.setPhone(sysUser.getPhone());
            sysUser1.setEmail(sysUser.getEmail());
            sysUser1.setAvatar(sysUser.getAvatar());
            sysUser1.setUpdateBy(UserContext.getCurrentUser().getUserId());
            sysUser1.setUpdateTime(new Date());
            int i = sysUserMapper.updateById(sysUser1);
            if (i>0){
                return Result.OK("修改成功");
            }else {
                return Result.ERROR("修改失败");
            }
        }else {
            return Result.ERROR("用户不存在");
        }
    }

    /**
     * 修改个人密码
     *
     * @param
     * @param
     */
    @Override
    public Result updateUserPassword(PasswordDto passwordDto) {
        Integer userId = UserContext.getCurrentUser().getUserId();
        BCryptPasswordEncoder bCryptPasswordEncoder=new BCryptPasswordEncoder();
        SysUser sysUser1 = sysUserMapper.selectById(userId);
        if (!bCryptPasswordEncoder.matches(passwordDto.getOldPassword(),sysUser1.getPassword())){
            return Result.ERROR("旧密码错误");
        }
        if (passwordDto!=null){
            if (passwordDto.getNewPassword().equals(passwordDto.getConfirmPassword())){
                SysUser sysUser = sysUserMapper.selectById(userId);
                BCryptPasswordEncoder bCryptPasswordEncoder1=new BCryptPasswordEncoder();
                String newPassword = bCryptPasswordEncoder1.encode(passwordDto.getNewPassword());
                sysUser.setPassword(newPassword);
                sysUser.setUpdateBy(UserContext.getCurrentUser().getUserId());
                sysUser.setUpdateTime(new Date());
                int i = sysUserMapper.updateById(sysUser);
                if (i>0){
                    return Result.OK("修改成功");
                }else {
                    return Result.ERROR("修改失败");
                }
            }else {
                return Result.ERROR("两种密码不匹配");
            }
        }else {
            return Result.ERROR("数据为空");
        }
    }

    /**
     * 查找用户详情
     *
     * @param
     */
    @Override
    public Result getUserDetail() {
        Integer userId = UserContext.getCurrentUser().getUserId();
        List<String> roleList=new ArrayList<>();
        QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id",userId);
        List<SysUserRole> sysUserRoleList = sysUserRoleMapper.selectList(queryWrapper);
        for (SysUserRole sysUserRole : sysUserRoleList) {
            Integer roleId = sysUserRole.getRoleId();
            QueryWrapper<SysRole> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("role_id",roleId);
            SysRole sysRole = sysRoleMapper.selectOne(queryWrapper1);
            roleList.add(sysRole.getRoleName());
        }
        SysUser sysUser = sysUserMapper.selectById(userId);
        sysUser.setRoleNames(Collections.singletonList(String.join(",", roleList)));
        if (sysUser!=null){
            return Result.OK(sysUser);
        }else {
            return Result.ERROR("用户不存在");
        }
    }

}

package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.PointsApply;
import com.zhentao.pojo.PointsRecord;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.PointsApplyService;
import com.zhentao.service.PointsRecordService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首页仪表盘数据控制器
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private EduClassService eduClassService;
    
    @Autowired
    private EduStudentService eduStudentService;
    
    @Autowired
    private PointsRecordService pointsRecordService;
    
    @Autowired
    private PointsApplyService pointsApplyService;
    
    /**
     * 获取首页统计数据
     */
    @GetMapping("/stats")
    public Result getStats() {
        try {
            Map<String, Object> statsMap = new HashMap<>();
            
            // 获取当前月份
            Calendar cal = Calendar.getInstance();
            int currentMonth = cal.get(Calendar.MONTH) + 1;
            int currentYear = cal.get(Calendar.YEAR);
            
            // 获取本月积分记录数
            LambdaQueryWrapper<PointsRecord> recordWrapper = new LambdaQueryWrapper<>();
            // 这里应该根据创建时间过滤本月的记录
            // 由于示例中没有完整的时间字段处理，这里先统计所有记录
            long monthlyRecords = pointsRecordService.count(recordWrapper);
            statsMap.put("monthlyRecords", monthlyRecords);
            
            // 处理进度百分比（模拟数据）
            int processPercentage = (int)(Math.random() * 100);
            statsMap.put("processPercentage", processPercentage);
            
            // 获取待审批和已处理的申请数量
            LambdaQueryWrapper<PointsApply> pendingWrapper = new LambdaQueryWrapper<>();
            pendingWrapper.eq(PointsApply::getStatus, 0); // 待审核状态
            long pendingApprovals = pointsApplyService.count(pendingWrapper);
            statsMap.put("pendingApprovals", pendingApprovals);
            
            LambdaQueryWrapper<PointsApply> processedWrapper = new LambdaQueryWrapper<>();
            processedWrapper.in(PointsApply::getStatus, Arrays.asList(1, 2)); // 已通过或已拒绝状态
            long processedApprovals = pointsApplyService.count(processedWrapper);
            statsMap.put("processedApprovals", processedApprovals);
            
            // 获取总积分
            LambdaQueryWrapper<EduStudent> studentWrapper = new LambdaQueryWrapper<>();
            List<EduStudent> students = eduStudentService.list(studentWrapper);
            int totalPoints = students.stream()
                .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                .sum();
            statsMap.put("totalPoints", totalPoints);
            
            return Result.OK(statsMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取统计数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取班级积分排名
     */
    @GetMapping("/class-rankings")
    public Result getClassRankings(@RequestParam(value = "limit", defaultValue = "5") Integer limit) {
        try {
            // 获取所有班级
            List<EduClass> classes = eduClassService.list();
            List<Map<String, Object>> rankings = new ArrayList<>();
            
            // 计算每个班级的总积分
            for (EduClass eduClass : classes) {
                // 获取班级学生
                LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(EduStudent::getClassId, eduClass.getClassId());
                List<EduStudent> students = eduStudentService.list(wrapper);
                
                // 计算班级总积分
                int totalPoints = students.stream()
                    .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                    .sum();
                
                Map<String, Object> classRank = new HashMap<>();
                classRank.put("classId", eduClass.getClassId());
                classRank.put("className", eduClass.getClassName());
                classRank.put("points", totalPoints);
                
                rankings.add(classRank);
            }
            
            // 按积分降序排序
            rankings.sort((a, b) -> {
                Integer pointsA = (Integer) a.get("points");
                Integer pointsB = (Integer) b.get("points");
                return pointsB.compareTo(pointsA);
            });
            
            // 添加排名
            for (int i = 0; i < rankings.size(); i++) {
                rankings.get(i).put("rank", i + 1);
            }
            
            // 限制返回数量
            if (rankings.size() > limit) {
                rankings = rankings.subList(0, limit);
            }
            
            return Result.OK(rankings);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级排名失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取最近活动
     */
    @GetMapping("/recent-activities")
    public Result getRecentActivities(@RequestParam(value = "limit", defaultValue = "5") Integer limit) {
        try {
            // 获取最近的积分记录
            LambdaQueryWrapper<PointsRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(PointsRecord::getOperationTime);
            wrapper.last("LIMIT " + limit);
            List<PointsRecord> records = pointsRecordService.list(wrapper);
            
            List<Map<String, Object>> activities = new ArrayList<>();
            
            for (PointsRecord record : records) {
                Map<String, Object> activity = new HashMap<>();
                
                // 获取学生信息
                QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
                studentWrapper.eq("student_no", record.getStudentNo());
                EduStudent student = eduStudentService.getOne(studentWrapper);
                String studentName = student != null ? student.getRealName() : "未知学生";
                
                // 构建活动内容
                String content;
                String type;
                boolean hollow = false;
                
                switch (record.getOperationType()) {
                    case 1: // 申请加分
                        content = studentName + " 申请了 +" + record.getPointsChange() + " 积分";
                        type = "primary";
                        break;
                    case 2: // 申请减分
                        content = studentName + " 被扣除了 " + Math.abs(record.getPointsChange()) + " 积分";
                        type = "warning";
                        break;
                    case 3: // 审核通过
                        content = "审核通过了 " + studentName + " 的积分变动";
                        type = "success";
                        break;
                    case 4: // 审核拒绝
                        content = "拒绝了 " + studentName + " 的积分申请";
                        type = "danger";
                        break;
                    case 5: // 系统调整
                        content = "系统调整了 " + studentName + " 的积分";
                        type = "info";
                        hollow = true;
                        break;
                    default:
                        content = "未知操作: " + studentName;
                        type = "info";
                }
                
                activity.put("content", content);
                activity.put("timestamp", record.getOperationTime() != null ? 
                        record.getOperationTime().toString() : new Date().toString());
                activity.put("type", type);
                activity.put("hollow", hollow);
                activity.put("size", "normal");
                
                activities.add(activity);
            }
            
            return Result.OK(activities);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取最近活动失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取积分申请统计
     */
    @GetMapping("/points-apply-stats")
    public Result getPointsApplyStats() {
        try {
            Map<String, Object> statsMap = new HashMap<>();
            
            // 获取各种状态的申请数量
            LambdaQueryWrapper<PointsApply> pendingWrapper = new LambdaQueryWrapper<>();
            pendingWrapper.eq(PointsApply::getStatus, 0); // 待审核
            long pendingCount = pointsApplyService.count(pendingWrapper);
            
            LambdaQueryWrapper<PointsApply> approvedWrapper = new LambdaQueryWrapper<>();
            approvedWrapper.eq(PointsApply::getStatus, 1); // 已通过
            long approvedCount = pointsApplyService.count(approvedWrapper);
            
            LambdaQueryWrapper<PointsApply> rejectedWrapper = new LambdaQueryWrapper<>();
            rejectedWrapper.eq(PointsApply::getStatus, 2); // 已拒绝
            long rejectedCount = pointsApplyService.count(rejectedWrapper);
            
            statsMap.put("pendingCount", pendingCount);
            statsMap.put("approvedCount", approvedCount);
            statsMap.put("rejectedCount", rejectedCount);
            statsMap.put("totalCount", pendingCount + approvedCount + rejectedCount);
            
            return Result.OK(statsMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分申请统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学生积分概览
     */
    @GetMapping("/student-points-overview")
    public Result getStudentPointsOverview() {
        try {
            Map<String, Object> overviewMap = new HashMap<>();
            
            // 获取所有学生
            List<EduStudent> students = eduStudentService.list();
            
            // 计算平均积分
            double averagePoints = students.stream()
                .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                .average()
                .orElse(0);
            
            // 获取最高和最低积分
            int maxPoints = students.stream()
                .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                .max()
                .orElse(0);
            
            int minPoints = students.stream()
                .filter(student -> student.getPoints() != null && student.getPoints() > 0)
                .mapToInt(EduStudent::getPoints)
                .min()
                .orElse(0);
            
            // 按积分区间统计学生数量
            int[] ranges = {0, 60, 70, 80, 90, 100};
            Map<String, Integer> distribution = new HashMap<>();
            
            for (int i = 0; i < ranges.length - 1; i++) {
                int min = ranges[i];
                int max = ranges[i + 1];
                String key = min + "-" + max;
                
                long count = students.stream()
                    .filter(student -> {
                        Integer points = student.getPoints();
                        return points != null && points >= min && points < max;
                    })
                    .count();
                
                distribution.put(key, (int) count);
            }
            
            // 超过100分的学生
            long above100 = students.stream()
                .filter(student -> student.getPoints() != null && student.getPoints() >= 100)
                .count();
            
            distribution.put("100+", (int) above100);
            
            overviewMap.put("totalStudents", students.size());
            overviewMap.put("averagePoints", averagePoints);
            overviewMap.put("maxPoints", maxPoints);
            overviewMap.put("minPoints", minPoints);
            overviewMap.put("distribution", distribution);
            
            return Result.OK(overviewMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取学生积分概览失败: " + e.getMessage());
        }
    }
} 
<template>
  <div class="application-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>申请管理</h2>
      <p>管理积分申请，查看申请状态和历史记录</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：快捷申请 -->
      <el-col :span="8">
        <!-- 申请添加积分 -->
        <el-card class="quick-apply-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Plus /></el-icon>
              <span>申请添加积分</span>
            </div>
          </template>
          <el-form :model="addPointsForm" :rules="addPointsRules" ref="addPointsFormRef" label-width="80px">
            <el-form-item label="申请类型" prop="type">
              <el-select v-model="addPointsForm.type" placeholder="请选择申请类型" style="width: 100%">
                <el-option label="学习成绩" value="study" />
                <el-option label="竞赛获奖" value="competition" />
                <el-option label="志愿服务" value="volunteer" />
                <el-option label="社会实践" value="practice" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item label="申请分值" prop="points">
              <el-input-number 
                v-model="addPointsForm.points" 
                :min="1" 
                :max="100" 
                style="width: 100%"
                placeholder="请输入申请分值"
              />
            </el-form-item>
            <el-form-item label="申请原因" prop="reason">
              <el-input 
                v-model="addPointsForm.reason" 
                type="textarea" 
                :rows="4"
                placeholder="请详细描述申请原因"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="证明材料">
              <el-upload
                class="upload-demo"
                :action="uploadAction"
                :file-list="addPointsForm.attachments"
                :on-change="handleFileChange"
                :auto-upload="false"
                multiple
              >
                <el-button size="small" type="primary">
                  <el-icon><Upload /></el-icon>
                  上传文件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png/pdf文件，且不超过2MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitAddPointsApplication" :loading="submitting">
                提交申请
              </el-button>
              <el-button @click="resetAddPointsForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 申请扣除积分 -->
        <el-card class="quick-apply-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Minus /></el-icon>
              <span>申请扣除积分</span>
            </div>
          </template>
          <el-form :model="deductPointsForm" :rules="deductPointsRules" ref="deductPointsFormRef" label-width="80px">
            <el-form-item label="扣除原因" prop="reason">
              <el-select v-model="deductPointsForm.reason" placeholder="请选择扣除原因" style="width: 100%">
                <el-option label="违纪行为" value="violation" />
                <el-option label="迟到早退" value="attendance" />
                <el-option label="作业未交" value="homework" />
                <el-option label="考试作弊" value="cheating" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item label="扣除分值" prop="points">
              <el-input-number 
                v-model="deductPointsForm.points" 
                :min="1" 
                :max="50" 
                style="width: 100%"
                placeholder="请输入扣除分值"
              />
            </el-form-item>
            <el-form-item label="详细说明" prop="description">
              <el-input 
                v-model="deductPointsForm.description" 
                type="textarea" 
                :rows="4"
                placeholder="请详细说明扣除原因"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="submitDeductPointsApplication" :loading="submitting">
                提交申请
              </el-button>
              <el-button @click="resetDeductPointsForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧：申请记录 -->
      <el-col :span="16">
        <el-card class="application-records-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>申请记录</span>
              <div class="header-actions">
                <el-button size="small" @click="refreshApplications">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>

          <!-- 筛选条件 -->
          <div class="filter-section">
            <el-form :inline="true" :model="applicationFilter" size="small">
              <el-form-item label="状态">
                <el-select v-model="applicationFilter.status" placeholder="全部状态" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="待审核" value="pending" />
                  <el-option label="已通过" value="approved" />
                  <el-option label="已拒绝" value="rejected" />
                  <el-option label="已撤销" value="cancelled" />
                </el-select>
              </el-form-item>
              <el-form-item label="类型">
                <el-select v-model="applicationFilter.type" placeholder="全部类型" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="加分" value="add" />
                  <el-option label="扣分" value="deduct" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间">
                <el-date-picker
                  v-model="applicationFilter.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="searchApplications">查询</el-button>
                <el-button size="small" @click="resetApplicationFilter">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 申请列表 -->
          <div class="application-list">
            <el-table :data="applicationRecords" stripe v-loading="loading">
              <el-table-column prop="id" label="申请编号" width="100" />
              <el-table-column prop="submitDate" label="提交时间" width="120" />
              <el-table-column prop="type" label="类型" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.type === 'add' ? 'success' : 'danger'" size="small">
                    {{ scope.row.type === 'add' ? '加分' : '扣分' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="category" label="分类" width="100" />
              <el-table-column prop="points" label="分值" width="80">
                <template #default="scope">
                  <span :class="scope.row.type === 'add' ? 'points-add' : 'points-deduct'">
                    {{ scope.row.type === 'add' ? '+' : '-' }}{{ scope.row.points }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="reason" label="申请原因" min-width="200" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)" size="small">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="reviewer" label="审核人" width="100" />
              <el-table-column prop="reviewDate" label="审核时间" width="120" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="viewApplicationDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button 
                    v-if="scope.row.status === 'pending'"
                    type="text" 
                    size="small" 
                    @click="cancelApplication(scope.row)"
                  >
                    撤销
                  </el-button>
                  <el-button 
                    v-if="scope.row.status === 'rejected'"
                    type="text" 
                    size="small" 
                    @click="resubmitApplication(scope.row)"
                  >
                    重新申请
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="applicationPagination.currentPage"
                v-model:page-size="applicationPagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="applicationPagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleApplicationPageSizeChange"
                @current-change="handleApplicationPageChange"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申请详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="申请详情" width="600px">
      <div v-if="selectedApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ selectedApplication.id }}</el-descriptions-item>
          <el-descriptions-item label="申请类型">
            <el-tag :type="selectedApplication.type === 'add' ? 'success' : 'danger'" size="small">
              {{ selectedApplication.type === 'add' ? '加分申请' : '扣分申请' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请分类">{{ selectedApplication.category }}</el-descriptions-item>
          <el-descriptions-item label="申请分值">
            <span :class="selectedApplication.type === 'add' ? 'points-add' : 'points-deduct'">
              {{ selectedApplication.type === 'add' ? '+' : '-' }}{{ selectedApplication.points }}分
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="提交时间" :span="2">{{ selectedApplication.submitDate }}</el-descriptions-item>
          <el-descriptions-item label="申请状态" :span="2">
            <el-tag :type="getStatusType(selectedApplication.status)">
              {{ getStatusText(selectedApplication.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请原因" :span="2">
            {{ selectedApplication.reason }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedApplication.reviewer" label="审核人">
            {{ selectedApplication.reviewer }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedApplication.reviewDate" label="审核时间">
            {{ selectedApplication.reviewDate }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedApplication.reviewComment" label="审核意见" :span="2">
            {{ selectedApplication.reviewComment }}
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 附件列表 -->
        <div v-if="selectedApplication.attachments && selectedApplication.attachments.length > 0" class="attachments-section">
          <h4>附件列表</h4>
          <el-list>
            <el-list-item v-for="file in selectedApplication.attachments" :key="file.id">
              <div class="attachment-item">
                <el-icon><Document /></el-icon>
                <span>{{ file.name }}</span>
                <el-button type="text" size="small" @click="downloadAttachment(file)">下载</el-button>
              </div>
            </el-list-item>
          </el-list>
        </div>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Minus, Document, Refresh, Upload 
} from '@element-plus/icons-vue'

// 表单引用
const addPointsFormRef = ref()
const deductPointsFormRef = ref()

// 提交状态
const submitting = ref(false)
const loading = ref(false)

// 上传地址
const uploadAction = ref('/api/upload')

// 加分申请表单
const addPointsForm = reactive({
  type: '',
  points: null,
  reason: '',
  attachments: []
})

// 加分申请表单验证规则
const addPointsRules = {
  type: [{ required: true, message: '请选择申请类型', trigger: 'change' }],
  points: [{ required: true, message: '请输入申请分值', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]
}

// 扣分申请表单
const deductPointsForm = reactive({
  reason: '',
  points: null,
  description: ''
})

// 扣分申请表单验证规则
const deductPointsRules = {
  reason: [{ required: true, message: '请选择扣除原因', trigger: 'change' }],
  points: [{ required: true, message: '请输入扣除分值', trigger: 'blur' }],
  description: [{ required: true, message: '请输入详细说明', trigger: 'blur' }]
}

// 申请筛选条件
const applicationFilter = reactive({
  status: '',
  type: '',
  dateRange: []
})

// 申请记录分页
const applicationPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 50
})

// 申请记录数据
const applicationRecords = ref([
  {
    id: 'APP001',
    submitDate: '2024-12-20',
    type: 'add',
    category: '学习成绩',
    points: 20,
    reason: '期末考试成绩优秀，获得专业第一名',
    status: 'approved',
    reviewer: '李老师',
    reviewDate: '2024-12-21',
    reviewComment: '成绩确实优秀，同意加分',
    attachments: [
      { id: 1, name: '成绩单.pdf' }
    ]
  },
  {
    id: 'APP002',
    submitDate: '2024-12-18',
    type: 'add',
    category: '志愿服务',
    points: 15,
    reason: '参与社区志愿服务活动',
    status: 'pending',
    reviewer: '',
    reviewDate: '',
    reviewComment: '',
    attachments: []
  },
  {
    id: 'APP003',
    submitDate: '2024-12-15',
    type: 'deduct',
    category: '违纪行为',
    points: 5,
    reason: '上课迟到',
    status: 'rejected',
    reviewer: '王老师',
    reviewDate: '2024-12-16',
    reviewComment: '情况属实，但考虑到是初犯，不予扣分',
    attachments: []
  }
])

// 申请详情对话框
const detailDialogVisible = ref(false)
const selectedApplication = ref(null)

// 方法
const handleFileChange = (file, fileList) => {
  addPointsForm.attachments = fileList
}

const submitAddPointsApplication = async () => {
  try {
    await addPointsFormRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    setTimeout(() => {
      ElMessage.success('加分申请提交成功，请等待审核')
      resetAddPointsForm()
      submitting.value = false
      refreshApplications()
    }, 1000)
  } catch (error) {
    console.log('表单验证失败')
  }
}

const resetAddPointsForm = () => {
  addPointsFormRef.value?.resetFields()
  addPointsForm.attachments = []
}

const submitDeductPointsApplication = async () => {
  try {
    await deductPointsFormRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    setTimeout(() => {
      ElMessage.success('扣分申请提交成功，请等待审核')
      resetDeductPointsForm()
      submitting.value = false
      refreshApplications()
    }, 1000)
  } catch (error) {
    console.log('表单验证失败')
  }
}

const resetDeductPointsForm = () => {
  deductPointsFormRef.value?.resetFields()
}

const refreshApplications = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 500)
}

const searchApplications = () => {
  console.log('搜索申请记录', applicationFilter)
}

const resetApplicationFilter = () => {
  applicationFilter.status = ''
  applicationFilter.type = ''
  applicationFilter.dateRange = []
}

const handleApplicationPageSizeChange = (size) => {
  applicationPagination.pageSize = size
}

const handleApplicationPageChange = (page) => {
  applicationPagination.currentPage = page
}

const viewApplicationDetail = (application) => {
  selectedApplication.value = application
  detailDialogVisible.value = true
}

const cancelApplication = (application) => {
  ElMessageBox.confirm('确定要撤销这个申请吗？', '确认撤销', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('申请已撤销')
    refreshApplications()
  })
}

const resubmitApplication = (application) => {
  ElMessage.info('重新申请功能开发中...')
}

const downloadAttachment = (file) => {
  ElMessage.info(`下载文件: ${file.name}`)
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝',
    'cancelled': '已撤销'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.application-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.header-actions {
  margin-left: auto;
}

/* 快捷申请卡片 */
.quick-apply-card {
  margin-bottom: 20px;
}

.quick-apply-card :deep(.el-form-item__label) {
  font-weight: 500;
}

/* 申请记录卡片 */
.application-records-card {
  min-height: 600px;
}

.filter-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.application-list {
  min-height: 400px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 积分颜色 */
.points-add {
  color: #67c23a;
  font-weight: bold;
}

.points-deduct {
  color: #f56c6c;
  font-weight: bold;
}

/* 申请详情 */
.application-detail {
  padding: 10px 0;
}

.attachments-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.attachments-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-item span {
  flex: 1;
}

/* 上传组件样式 */
.upload-demo :deep(.el-upload__tip) {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section .el-form {
    display: block;
  }
  
  .filter-section .el-form-item {
    margin-bottom: 10px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    margin-left: 0;
  }
}
</style>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API状态测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .loading { background-color: #fff3cd; }
        button { padding: 8px 16px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API状态测试工具</h1>
    
    <div class="test-item">
        <h3>1. 基础API测试</h3>
        <button onclick="testBasicAPI()">测试基础API</button>
        <div id="basic-result"></div>
    </div>
    
    <div class="test-item">
        <h3>2. 学生列表API测试</h3>
        <button onclick="testStudentList()">测试学生列表</button>
        <div id="student-result"></div>
    </div>
    
    <div class="test-item">
        <h3>3. 班级选项API测试</h3>
        <button onclick="testClassOptions()">测试班级选项</button>
        <div id="class-result"></div>
    </div>
    
    <div class="test-item">
        <h3>4. ID自增重置API测试</h3>
        <button onclick="testResetAutoIncrement()">测试快速重置</button>
        <button onclick="testCustomReset()">测试自定义重置</button>
        <div id="reset-result"></div>
    </div>
    
    <div class="test-item">
        <h3>5. 批量导入API测试</h3>
        <button onclick="testBatchImport()">测试批量导入</button>
        <div id="import-result"></div>
    </div>

    <script>
        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('Authorization');
            return {
                'Authorization': token || '',
                'Content-Type': 'application/json'
            };
        }

        // 显示结果
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = success ? 'success' : 'error';
            
            let html = `<p><strong>${success ? '✅ 成功' : '❌ 失败'}:</strong> ${message}</p>`;
            if (data) {
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            element.innerHTML = html;
        }

        // 显示加载状态
        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'loading';
            element.innerHTML = `<p>🔄 ${message}</p>`;
        }

        // 1. 测试基础API
        async function testBasicAPI() {
            showLoading('basic-result', '测试基础连接...');
            
            try {
                const response = await fetch('/edu-student/getClassOptions', {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('basic-result', true, `API连接正常 (${response.status})`, data);
                } else {
                    showResult('basic-result', false, `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('basic-result', false, `网络错误: ${error.message}`);
            }
        }

        // 2. 测试学生列表
        async function testStudentList() {
            showLoading('student-result', '获取学生列表...');
            
            try {
                const response = await fetch('/edu-student/findPage', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        current: 1,
                        size: 10
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('student-result', true, `学生列表获取成功 (${response.status})`, {
                        code: data.code,
                        message: data.message,
                        total: data.data?.total || 0
                    });
                } else {
                    showResult('student-result', false, `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('student-result', false, `请求失败: ${error.message}`);
            }
        }

        // 3. 测试班级选项
        async function testClassOptions() {
            showLoading('class-result', '获取班级选项...');
            
            try {
                const response = await fetch('/edu-student/getClassOptions', {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('class-result', true, `班级选项获取成功 (${response.status})`, data);
                } else {
                    showResult('class-result', false, `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('class-result', false, `请求失败: ${error.message}`);
            }
        }

        // 4. 测试ID自增重置
        async function testResetAutoIncrement() {
            showLoading('reset-result', '执行快速重置...');
            
            try {
                const response = await fetch('/edu-student/reset-student-auto-increment', {
                    method: 'POST',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('reset-result', true, `快速重置成功 (${response.status})`, data);
                } else {
                    const errorText = await response.text();
                    showResult('reset-result', false, `HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                showResult('reset-result', false, `请求失败: ${error.message}`);
            }
        }

        // 5. 测试自定义重置
        async function testCustomReset() {
            showLoading('reset-result', '执行自定义重置...');
            
            try {
                const response = await fetch('/edu-student/reset-auto-increment?resetStudentTable=true&resetClassTable=false', {
                    method: 'POST',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('reset-result', true, `自定义重置成功 (${response.status})`, data);
                } else {
                    const errorText = await response.text();
                    showResult('reset-result', false, `HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                showResult('reset-result', false, `请求失败: ${error.message}`);
            }
        }

        // 6. 测试批量导入
        async function testBatchImport() {
            showLoading('import-result', '测试批量导入...');
            
            // 创建测试数据
            const testData = {
                classes: [
                    {
                        className: "测试班级001",
                        counselor: "测试辅导员",
                        teacher: "测试讲师",
                        classroom: "测试教室101",
                        stageName: "测试阶段",
                        courseName: "测试课程",
                        students: [
                            {
                                studentNo: "TEST001",
                                realName: "测试学生001"
                            }
                        ]
                    }
                ]
            };
            
            try {
                const response = await fetch('/edu-student/batch/import', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('import-result', true, `批量导入测试成功 (${response.status})`, data);
                } else {
                    const errorText = await response.text();
                    showResult('import-result', false, `HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                showResult('import-result', false, `请求失败: ${error.message}`);
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('API测试工具已加载');
            console.log('请确保：');
            console.log('1. 后端服务正在运行');
            console.log('2. 已登录并获取Authorization token');
            console.log('3. 数据库连接正常');
            
            // 检查是否有Authorization token
            const token = localStorage.getItem('Authorization');
            if (!token) {
                alert('警告：未找到Authorization token，请先登录系统');
            }
        };
    </script>
</body>
</html>

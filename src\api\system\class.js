import request from '@/request';

/*
 * 分页获取班级列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getClassList(params) {
  const token = localStorage.getItem("Authorization");
  if (!token) {
    return Promise.reject(new Error('未登录或登录已过期'));
  }
  
  // 清理参数
  const cleanParams = { ...params };
  
  // 移除空值参数
  Object.keys(cleanParams).forEach(key => {
    if (cleanParams[key] === null || cleanParams[key] === undefined || cleanParams[key] === '') {
      delete cleanParams[key];
    }
  });
  
  return request({
    url: '/edu-class/findPage',
    method: 'post',
    headers: {
      'Authorization': token,
      'Content-Type': 'application/json'
    },
    data: cleanParams
  });
}

/*
 * 添加班级
 * @param {Object} data 班级信息
 * @returns {Promise}
 */
export function addClass(data) {
  return request({
    url: '/edu-class/addClass',
    method: 'post',
    data
  });
}

/*
 * 更新班级信息
 * @param {Object} data 班级信息
 * @returns {Promise}
 */
export function updateClass(data) {
  // 创建一个新对象，避免修改原始数据
  const formData = { ...data };
  
  // 处理日期格式，后端需要Date对象或yyyy-MM-dd格式
  if (formData.startDate) {
    // 不做特殊处理，保持原格式
    console.log('发送开班日期:', formData.startDate);
  }
  
  if (formData.endDate) {
    // 不做特殊处理，保持原格式
    console.log('发送结束日期:', formData.endDate);
  }
  
  // 确保有必要的字段
  if (!formData.classId) {
    return Promise.reject(new Error('班级ID不能为空'));
  }
  
  // 移除可能导致问题的字段
  delete formData.pageNum;
  delete formData.pageSize;
  delete formData.students;
  
  console.log('发送更新班级请求数据:', JSON.stringify(formData));
  
  return request({
    url: '/edu-class/updateClass',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: formData
  });
}

/*
 * 删除班级
 * @param {Number} id 班级ID
 * @returns {Promise}
 */
export function deleteClass(id) {
  return request({
    url: '/edu-class/delClass',
    method: 'get',
    params: { id }
  });
}

/*
 * 获取班级学生列表
 * @param {Number} classId 班级ID
 * @returns {Promise}
 */
export function getClassStudents(classId) {
  return request({
    url: '/edu-student/findByClassId',
    method: 'get',
    params: { classId }
  });
}

/*
 * 获取专业列表
 * @returns {Promise}
 */
export function getStageList() {
  return request({
    url: '/edu-stage/getSelectableStages',
    method: 'get'
  });
}

/*
 * 获取班级统计信息
 * @param {Number} classId 班级ID
 * @returns {Promise}
 */
export function getClassStatistics(classId) {
  return request({
    url: '/edu-class/getClassStatistics',
    method: 'get',
    params: { classId }
  });
}

/*
 * 导出班级学生数据
 * @param {Number} classId 班级ID
 * @returns {Promise}
 */
export function exportClassStudents(classId) {
  return request({
    url: '/edu-class/exportStudents',
    method: 'get',
    params: { classId },
    responseType: 'blob'
  });
}

/*
 * 根据专业ID获取班级列表
 * @param {Number} stageId 专业ID
 * @returns {Promise}
 */
export function getClassesByStage(stageId) {
  return request({
    url: '/edu-class/findByStageId',
    method: 'get',
    params: { stageId }
  });
}

/*
 * 查询所有班级名称
 */
export function findAllClassNames() {
  return request({
    url: '/edu-class/findAllClassNames',
    method: 'post'
  });
}

/*
 * 获取班级积分趋势
 * @param {Array} classIds 班级ID数组，可选
 * @returns {Promise}
 */
export function getClassPointsTrend(classIds) {
  return request({
    url: '/statistics/class-trend',
    method: 'get',
    params: classIds ? { classIds: classIds.join(',') } : {}
  });
}

//-------------------------------------以下代码都是cmy写的------------------------------------------------

/**
 * 查询积分平均数再前三名的班级
 */
export function queryTopThreeClass() {
  return request({
    url: '/edu-class/queryTopThreeClass',
    method: 'post'
  });
}
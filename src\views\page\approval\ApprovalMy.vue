<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
const classList=ref([])
const role=localStorage.getItem("roleId")
function getClass(){
  banji().then(res=>{
    classList.value=res.data.data
  })
}getClass()
import {banji, DYlist2, GZRlist2, JSlist2, YZlist2, YZRlist2} from "@/api/shenpi.js";
const list2Select=ref({
  applyUserId:"",
  pointsChange:"",
  classId:"",
  status2:"",
  pageNum:1,
  pageSize:5
})
const total=ref()
const pagedTableData=ref([])
function getlist2(){
  if (role.includes("1")){
    YZlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role.includes("3")&&role.includes("4")){
    GZRlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if(role.includes("3")&&role.includes("5")){
    GZRlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role.includes("2")&&role.includes("4")){
    YZRlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role.includes("2")&&role.includes("5")){
    YZRlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role=="4"){
    JSlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }else if (role=="5"){
    DYlist2(list2Select.value).then(res=>{
      pagedTableData.value=res.data.data.records
      total.value=res.data.data.total
    })
  }
}
getlist2()

// 筛选表单
const filterForm = reactive({
  type: '',
  status: '',
  dateRange: []
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref({})
const cancelDialogVisible = ref(false)
const cancelReason = ref('')

// 计算属性
const filteredTableData = computed(() => {
  let result = tableData.value
  
  // 按申请类型筛选
  if (filterForm.type) {
    result = result.filter(item => item.type === filterForm.type)
  }
  
  // 按状态筛选
  if (filterForm.status) {
    result = result.filter(item => item.status === filterForm.status)
  }
  
  // 按日期范围筛选
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const startDate = new Date(filterForm.dateRange[0])
    const endDate = new Date(filterForm.dateRange[1])
    endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间
    
    result = result.filter(item => {
      const itemDate = new Date(item.createTime)
      return itemDate >= startDate && itemDate <= endDate
    })
  }
  
  return result
})

// const pagedTableData = computed(() => {
//   const start = (currentPage.value - 1) * pageSize.value
//   const end = start + pageSize.value
//   return filteredTableData.value.slice(start, end)
// })

// 生命周期钩子
// onMounted(() => {
//   loadData()
// })

// 方法定义
const handleSearch = () => {
  currentPage.value = 1
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'dateRange') {
      filterForm[key] = []
    } else {
      filterForm[key] = ''
    }
  })
  currentPage.value = 1
}

// const loadData = () => {
//   loading.value = true
//   // 模拟加载数据
//   setTimeout(() => {
//     tableData.value = generateMockData()
//     loading.value = false
//   }, 500)
// }

const refreshData = () => {
  loadData()
}

const handleSizeChange = (val) => {
  getlist2()
}

const handleCurrentChange = (val) => {
  getlist2()
}
const xq=ref([])
const viewDetail = (row) => {
  xq.value=row
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
}

const getStatusType = (status) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'cancelled': return 'info'
    default: return ''
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'pending': return '待审批'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'cancelled': return '已撤销'
    default: return '未知'
  }
}

const getTypeText = (type) => {
  switch (type) {
    case 'add': return '积分增加'
    case 'deduct': return '积分扣除'
    default: return '未知'
  }
}

const closeDetailDialog = () => {
  detailDialogVisible.value = false
}

const showCancelDialog = (row) => {
  currentDetail.value = { ...row }
  cancelReason.value = ''
  cancelDialogVisible.value = true
}

const submitCancel = () => {
  if (!cancelReason.value) {
    ElMessage.warning('请输入撤销原因')
    return
  }
  
  ElMessageBox.confirm(
    '确定要撤销此申请吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    // 模拟撤销操作
    const index = tableData.value.findIndex(item => item.id === currentDetail.value.id)
    if (index !== -1) {
      tableData.value[index].status = 'cancelled'
      tableData.value[index].cancelReason = cancelReason.value
      tableData.value[index].cancelTime = new Date().toLocaleString()
    }
    
    ElMessage.success('申请已撤销')
    cancelDialogVisible.value = false
  })
  .catch(() => {})
}

// // 生成模拟数据
// const generateMockData = () => {
//   const types = ['add', 'deduct']
//   const statuses = ['pending', 'approved', 'rejected', 'cancelled']
//   const reasons = [
//     '参加志愿活动',
//     '获得学科竞赛奖项',
//     '担任班级干部',
//     '违反课堂纪律',
//     '宿舍卫生不合格',
//     '迟到早退'
//   ]
//   const approvers = ['王主任', '李老师', '张主任', '陈老师']
//
//   const data = []
//
//   for (let i = 1; i <= 30; i++) {
//     const type = types[Math.floor(Math.random() * types.length)]
//     const status = statuses[Math.floor(Math.random() * statuses.length)]
//     const createDate = new Date()
//     createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 30))
//
//     const item = {
//       id: `AP${String(i).padStart(4, '0')}`,
//       type,
//       title: `${type === 'add' ? '积分增加' : '积分扣除'}申请`,
//       points: type === 'add' ? Math.floor(Math.random() * 10) + 1 : -(Math.floor(Math.random() * 10) + 1),
//       reason: reasons[Math.floor(Math.random() * reasons.length)],
//       createTime: createDate.toLocaleString(),
//       status,
//       attachments: Math.random() > 0.7 ? ['证明材料.jpg'] : []
//     }
//
//     if (status === 'approved' || status === 'rejected') {
//       const processDate = new Date(createDate)
//       processDate.setDate(processDate.getDate() + Math.floor(Math.random() * 3) + 1)
//       item.processTime = processDate.toLocaleString()
//       item.approver = approvers[Math.floor(Math.random() * approvers.length)]
//       item.comment = status === 'approved'
//         ? '符合申请条件，予以通过'
//         : '材料不足，无法证明申请理由'
//     }
//
//     if (status === 'cancelled') {
//       const cancelDate = new Date(createDate)
//       cancelDate.setDate(cancelDate.getDate() + Math.floor(Math.random() * 2) + 1)
//       item.cancelTime = cancelDate.toLocaleString()
//       item.cancelReason = '信息填写有误，需重新提交'
//     }
//
//     data.push(item)
//   }
//
//   return data
// }
</script>

<template>
  <div class="approval-my-container">
    <div class="page-header">
      <h2>我的申请</h2>
      <el-button type="primary" :icon="Refresh" circle @click="refreshData" />
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :model="list2Select" inline>
        <el-form-item label="申请类型">
          <el-select v-model="list2Select.pointsChange" placeholder="全部类型" @change="getlist2">
            <el-option label="积分增加" value=1 />
            <el-option label="积分扣除" value=2 />
          </el-select>
        </el-form-item>
        
        <el-form-item label="最终申请状态">
          <el-select v-model="list2Select.status2" placeholder="全部状态" @change="getlist2">
            <el-option label="待审批" value=1 />
            <el-option label="已通过" value=2 />
            <el-option label="已拒绝" value=3 />
          </el-select>
        </el-form-item>
        <el-form-item label="班级">
          <el-select v-model="list2Select.classId" placeholder="请选择班级" style="width: 180px" @change="getlist2">
            <el-option v-for="item in classList" :key="item.className" :label="item.className" :value="item.classId"></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="申请日期">-->
<!--          <el-date-picker-->
<!--            v-model="filterForm.dateRange"-->
<!--            type="daterange"-->
<!--            range-separator="至"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--            value-format="YYYY-MM-DD"-->
<!--          />-->
<!--        </el-form-item>-->
        
        <el-form-item>
          <el-button type="primary" @click="getlist2">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="pagedTableData"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="120" />
        <el-table-column #default="scope" label="学生姓名" width="120" >
          {{scope.row.student.realName}}
        </el-table-column>
        <el-table-column #default="scope" label="班级" width="120" >
         <span v-if="scope.row.eduClass!=null">{{scope.row.eduClass.className}}</span>
        </el-table-column>
        <el-table-column #default="scope" label="申请人" width="120" >
          <span v-if="scope.row.applyUser!=null"> {{scope.row.applyUser.username}}</span>
        </el-table-column>
        <el-table-column #default="scope" label="执行操作" width="120" >
          <span v-if="scope.row.pointsChange==1" style="color: green">加分</span>
          <span v-if="scope.row.pointsChange==2" style="color: red">减分</span>
        </el-table-column>
        <el-table-column prop="points" label="分值" width="120" />
        <el-table-column prop="reason" label="申请理由" width="120" />
        <el-table-column #default="scope" label="图片" width="120" >
          <el-image style="width: 100px; height: 100px" :src="scope.row.evidenceImages" />
        </el-table-column>
        <el-table-column #default="scope" label="导员讲师审批状态" width="120">
          <span v-if="scope.row.status==1">待审核</span>
          <span v-if="scope.row.status==2">已通过</span>
          <span v-if="scope.row.status==3">已拒绝</span>
        </el-table-column>
        <el-table-column #default="scope" label="主任审批状态" width="120">
          <span v-if="scope.row.status1==1">待审核</span>
          <span v-if="scope.row.status1==2">已通过</span>
          <span v-if="scope.row.status1==3">已拒绝</span>
        </el-table-column>
        <el-table-column #default="scope" label="院长审批状态" width="120">
          <span v-if="scope.row.status2==1">待审核</span>
          <span v-if="scope.row.status2==2">已通过</span>
          <span v-if="scope.row.status2==3">已拒绝</span>
        </el-table-column>
        <el-table-column #default="scope" label="审核人" width="120" >
             <span v-if="scope.row.reviewer!=null">  {{scope.row.reviewer.username}}</span>
        </el-table-column>
        <el-table-column prop="reviewTime" label="审核时间" width="120" />
        <el-table-column prop="reviewComment" label="审核意见" width="120" />
        <el-table-column #default="scope" label="创建人" width="120" >
            <span v-if="scope.row.createUser!=null"> {{scope.row.createUser.username}}</span>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column #default="scope" label="修改人" width="120" >
           <span v-if="scope.row.updateUser!=null">{{scope.row.updateUser.username}}</span>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="120" />
        <el-table-column prop="remark" label="备注" width="120" />
        <el-table-column #default="scope" label="删除标志" width="120" >
          <span v-if="scope.row.delFlag==1">存在</span>
          <span v-if="scope.row.delFlag==2">删除</span>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">查看</el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="danger" 
              link 
              @click="showCancelDialog(scope.row)"
            >
              撤销
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
            v-model:current-page="list2Select.pageNum"
            v-model:page-size="list2Select.pageSize"
            :page-sizes="[5, 10, 15, 20]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      width="600px"
      title="申请详情"
    >
      <el-form :model="xq" label-width="auto" style="max-width: 600px">
        <el-form-item label="学生姓名">
          <el-input v-model="xq.student.realName" />
        </el-form-item>
        <el-form-item label="班级">
          <el-input v-model="xq.eduClass.className" />
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model="xq.applyUser.username" />
        </el-form-item>
        <el-form-item label="执行操作">
          <span v-if="xq.pointsChange==1">加分</span>
          <span v-if="xq.pointsChange==2">减分</span>
        </el-form-item>
        <el-form-item label="分值">
          <el-input v-model="xq.points" />
        </el-form-item>
        <el-form-item label="申请理由">
          <el-input v-model="xq.reason" />
        </el-form-item>
        <el-form-item label="图片">
          <el-image style="width: 100px; height: 100px" :src="xq.evidenceImages" />
        </el-form-item>
        <el-form-item label="状态">
          <span v-if="xq.status==1">待审核</span>
          <span v-if="xq.status==2">已通过</span>
          <span v-if="xq.status==3">已拒绝</span>
        </el-form-item>
        <el-form-item label="审核人">
          <el-input v-model="xq.reviewer.username" />
        </el-form-item>
        <el-form-item label="审核时间">
          <el-input v-model="xq.reviewTime" />
        </el-form-item>
        <el-form-item label="创建人">
          <el-input v-model="xq.createUser.username" />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-input v-model="xq.createTime" />
        </el-form-item>
        <el-form-item label="更新人">
          <el-input v-model="xq.updateUser.username" />
        </el-form-item>
        <el-form-item label="更新时间">
          <el-input v-model="xq.updateTime" />
        </el-form-item>
        <el-form-item label="删除标志">
          <span v-if="xq.delFlag==1">存在</span>
          <span v-if="xq.delFlag==2">删除</span>
        </el-form-item>
      </el-form>
    </el-dialog>
    
    <!-- 撤销对话框 -->
    <el-dialog
      v-model="cancelDialogVisible"
      title="撤销申请"
      width="500px"
    >
      <el-form>
        <el-form-item label="撤销原因" required>
          <el-input
            v-model="cancelReason"
            type="textarea"
            rows="3"
            placeholder="请输入撤销原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCancel">确定撤销</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.approval-my-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.filter-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.positive {
  color: #67c23a;
  font-weight: bold;
}

.negative {
  color: #f56c6c;
  font-weight: bold;
}

.attachment-item {
  margin-bottom: 5px;
}

/* 确保内容可滚动 */
.approval-my-container {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  scrollbar-width: thin;
}

.approval-my-container::-webkit-scrollbar {
  width: 6px;
}

.approval-my-container::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.approval-my-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

/* 详情对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 14px;
  box-shadow: 0 8px 32px rgba(60, 120, 200, 0.18);
  background: linear-gradient(135deg, #fafdff 60%, #eaf3fa 100%);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 10px;
  background: linear-gradient(90deg, #3a7bd5, #00d2ff);
  margin-right: 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__headerbtn) {
  top: 18px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

:deep(.el-dialog__body) {
  padding: 24px 32px 10px 32px;
  max-height: 65vh;
  overflow-y: auto;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 10px;
}

:deep(.el-form-item:last-child) {
  border-bottom: none;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #3a7bd5;
  font-size: 15px;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
  background-color: transparent;
}

:deep(.el-input__inner) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-form-item span) {
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
}

:deep(.el-form-item span[v-if="xq.status==1"]) {
  background-color: #e6f7ff;
  color: #1890ff;
}

:deep(.el-form-item span[v-if="xq.status==2"]) {
  background-color: #f6ffed;
  color: #52c41a;
}

:deep(.el-form-item span[v-if="xq.status==3"]) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

:deep(.el-form-item span[v-if="xq.pointsChange==1"]) {
  background-color: #f6ffed;
  color: #52c41a;
}

:deep(.el-form-item span[v-if="xq.pointsChange==2"]) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

:deep(.el-image) {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

:deep(.el-image:hover) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(58, 123, 213, 0.2);
}

/* 撤销对话框样式 */
:deep(.el-dialog[title="撤销申请"]) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog[title="撤销申请"] .el-dialog__header) {
  background: linear-gradient(90deg, #f56c6c, #ff9a9a);
}

:deep(.el-dialog[title="撤销申请"] .el-dialog__footer) {
  padding: 10px 20px 20px;
  text-align: right;
}

:deep(.dialog-footer) {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 600px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 10px auto !important;
  }
  
  :deep(.el-dialog__body) {
    padding: 15px !important;
    max-height: 70vh;
  }
  
  :deep(.el-form-item__label) {
    padding-bottom: 8px;
  }
}
</style>
# 学生门户系统

## 概述

学生门户系统是一个专为学生设计的综合性服务平台，提供积分管理、学习记录、申请管理等功能。系统采用单页面应用设计，通过导航标签页切换不同功能模块。

## 功能模块

### 1. 首页 (HomePage)

**主要功能：**
- **排行榜**：展示积分加减数据，或班级排名数据等（支持论）
- **荣誉墙**：展示我们学院获得的一些荣誉，以及学生个人获得的一些荣誉
- **通知公告**：在这里可以展示学生的违纪情况，以及可以展示一些表现优异的学生，对其进行公告展示

**功能描述：**
- 显示学生基本信息和当前积分状态
- 实时积分排行榜（班级/年级切换）
- 个人荣誉展示墙，支持分类筛选
- 最新通知公告列表
- 快捷操作入口

**界面特点：**
- 欢迎横幅显示个人信息和积分统计
- 卡片式布局，信息层次清晰
- 响应式设计，支持移动端访问

### 2. 学生明细 (StudentInfoPage)

**主要功能：**
- **学生信息**：默认可以查看录入学生的积分情况，比如加减分评情等

**功能描述：**
- 个人档案信息管理
- 积分记录查询和筛选
- 学习情况统计（GPA、学分、课程成绩）
- 活动参与记录和获奖情况

**界面特点：**
- 左侧个人信息卡片和积分统计
- 右侧标签页展示详细记录
- 支持时间范围和类型筛选
- 数据可视化展示

### 3. 申请管理 (ApplicationPage)

**主要功能：**
- **申请添加**：再此可以为学生申请加分操作，可以批量申请（只有秘书可以操作此页面）
- **申请扣除**：在这里可以为学生申请扣除积分操作，可以批量申请（只有秘书可以操作此页面）
- **申请记录**：这里可以看到提交的所有申请记录（只有秘书可见）

**功能描述：**
- 在线提交加分/扣分申请
- 上传证明材料附件
- 查看申请状态和审核进度
- 申请记录管理（撤销、重新申请）

**界面特点：**
- 左侧快捷申请表单
- 右侧申请记录列表
- 支持文件上传和预览
- 申请详情弹窗展示

### 4. 关于我们 (AboutPage)

**主要功能：**
- **团队信息**：对开发团队进行简单介绍或展示

**功能描述：**
- 系统功能介绍和技术架构
- 开发团队成员展示
- 联系方式和意见反馈
- 版本信息和更新日志

**界面特点：**
- 系统介绍和核心功能展示
- 团队成员卡片式展示
- 多种联系方式提供
- 在线反馈表单

## 技术实现

### 前端技术栈
- **Vue 3**: 采用 Composition API
- **Element Plus**: UI 组件库
- **Vue Router**: 路由管理
- **Pinia**: 状态管理（如需要）

### 组件结构
```
studentPageList/
├── StudentPortal.vue          # 主入口组件
├── components/
│   ├── HomePage.vue           # 首页组件
│   ├── StudentInfoPage.vue    # 学生明细组件
│   ├── ApplicationPage.vue    # 申请管理组件
│   └── AboutPage.vue          # 关于我们组件
└── README.md                  # 说明文档
```

### 设计特点

1. **单页面应用**：所有功能模块在同一个页面中展示，通过导航切换
2. **响应式设计**：支持桌面端和移动端访问
3. **模块化开发**：每个功能模块独立组件，便于维护
4. **用户友好**：直观的界面设计和流畅的交互体验

## 使用说明

### 访问路径
```
/dashboard/student/portal
```

### 导航使用
- 点击顶部导航菜单切换不同功能模块
- 支持键盘快捷键导航
- 移动端支持触摸滑动切换

### 功能操作
1. **首页**：查看个人信息、排行榜、荣誉和通知
2. **学生明细**：查看和管理个人详细信息
3. **申请管理**：提交和管理各类申请
4. **关于我们**：了解系统和团队信息

## 权限说明

- **学生用户**：可访问所有功能模块，但申请管理中的某些操作可能受限
- **秘书用户**：在申请管理模块有特殊权限，可进行批量操作
- **管理员**：拥有所有权限，可管理系统设置

## 扩展性

系统采用模块化设计，便于后续功能扩展：
- 可轻松添加新的功能模块
- 支持权限控制和角色管理
- 可集成第三方服务和API
- 支持主题定制和个性化设置

## 注意事项

1. 确保网络连接稳定，以获得最佳使用体验
2. 上传文件时请注意文件大小和格式限制
3. 重要操作前请仔细确认，避免误操作
4. 如遇问题请及时联系技术支持团队
